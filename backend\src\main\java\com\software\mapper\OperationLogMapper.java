package com.software.mapper;

import com.software.entity.OperationLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 操作记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper
public interface OperationLogMapper {
    
    /**
     * 插入操作记录
     */
    @Insert("INSERT INTO operation_log (admin_id, admin_username, operation_type, operation_module, " +
            "operation_desc, target_id, target_name, request_method, request_url, request_params, " +
            "ip_address, user_agent, operation_status, error_message, execution_time, create_time) " +
            "VALUES (#{adminId}, #{adminUsername}, #{operationType}, #{operationModule}, " +
            "#{operationDesc}, #{targetId}, #{targetName}, #{requestMethod}, #{requestUrl}, #{requestParams}, " +
            "#{ipAddress}, #{userAgent}, #{operationStatus}, #{errorMessage}, #{executionTime}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(OperationLog operationLog);
    
    /**
     * 根据管理员ID获取操作记录列表（分页）
     */
    @Select("SELECT * FROM operation_log WHERE admin_id = #{adminId} ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<OperationLog> selectByAdminId(@Param("adminId") Long adminId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据管理员ID获取操作记录总数
     */
    @Select("SELECT COUNT(*) FROM operation_log WHERE admin_id = #{adminId}")
    int countByAdminId(@Param("adminId") Long adminId);
    
    /**
     * 获取最近的操作记录（限制数量）
     */
    @Select("SELECT * FROM operation_log WHERE admin_id = #{adminId} ORDER BY create_time DESC LIMIT #{limit}")
    List<OperationLog> selectRecentByAdminId(@Param("adminId") Long adminId, @Param("limit") int limit);
    
    /**
     * 根据操作类型获取操作记录
     */
    @Select("SELECT * FROM operation_log WHERE admin_id = #{adminId} AND operation_type = #{operationType} ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<OperationLog> selectByAdminIdAndType(@Param("adminId") Long adminId, @Param("operationType") String operationType, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据操作模块获取操作记录
     */
    @Select("SELECT * FROM operation_log WHERE admin_id = #{adminId} AND operation_module = #{operationModule} ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<OperationLog> selectByAdminIdAndModule(@Param("adminId") Long adminId, @Param("operationModule") String operationModule, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据条件获取操作记录（支持多条件筛选）
     */
    @Select("<script>" +
            "SELECT * FROM operation_log WHERE admin_id = #{adminId} " +
            "<if test='operationType != null and operationType != \"\"'>" +
            "AND operation_type = #{operationType} " +
            "</if>" +
            "<if test='operationModule != null and operationModule != \"\"'>" +
            "AND operation_module = #{operationModule} " +
            "</if>" +
            "ORDER BY create_time DESC LIMIT #{offset}, #{limit}" +
            "</script>")
    List<OperationLog> selectByCondition(@Param("adminId") Long adminId,
                                        @Param("operationType") String operationType,
                                        @Param("operationModule") String operationModule,
                                        @Param("offset") int offset,
                                        @Param("limit") int limit);

    /**
     * 根据条件统计操作记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM operation_log WHERE admin_id = #{adminId} " +
            "<if test='operationType != null and operationType != \"\"'>" +
            "AND operation_type = #{operationType} " +
            "</if>" +
            "<if test='operationModule != null and operationModule != \"\"'>" +
            "AND operation_module = #{operationModule} " +
            "</if>" +
            "</script>")
    int countByCondition(@Param("adminId") Long adminId,
                        @Param("operationType") String operationType,
                        @Param("operationModule") String operationModule);

    /**
     * 删除指定天数之前的操作记录（用于定期清理）
     */
    @Delete("DELETE FROM operation_log WHERE create_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int deleteOldRecords(@Param("days") int days);
    
    /**
     * 根据ID删除操作记录
     */
    @Delete("DELETE FROM operation_log WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 获取所有操作记录（管理员查看，支持分页）
     */
    @Select("SELECT * FROM operation_log ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<OperationLog> selectAll(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 获取操作记录总数
     */
    @Select("SELECT COUNT(*) FROM operation_log")
    int countAll();
}
