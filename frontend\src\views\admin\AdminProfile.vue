<template>
  <div class="admin-profile">
    <el-row :gutter="20">
      <!-- 个人信息卡片 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <div slot="header" class="card-header">
            <span>个人信息</span>
            <el-button 
              type="text" 
              @click="editMode = !editMode"
              :icon="editMode ? 'el-icon-close' : 'el-icon-edit'"
            >
              {{ editMode ? '取消编辑' : '编辑信息' }}
            </el-button>
          </div>
          
          <el-form 
            :model="adminForm" 
            :rules="adminRules" 
            ref="adminForm" 
            label-width="100px"
            :disabled="!editMode"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="adminForm.username" disabled />
              <div class="form-tip">用户名不可修改</div>
            </el-form-item>
            
            <el-form-item label="昵称" prop="nickname">
              <el-input 
                v-model="adminForm.nickname" 
                placeholder="请输入昵称"
              />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input 
                v-model="adminForm.email" 
                placeholder="请输入邮箱地址"
              />
            </el-form-item>
            
            <el-form-item label="手机号" prop="phone">
              <el-input 
                v-model="adminForm.phone" 
                placeholder="请输入手机号"
              />
            </el-form-item>
            
            <el-form-item label="头像" prop="avatar">
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl + '/admin/upload/image'"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                :disabled="!editMode"
              >
                <img v-if="adminForm.avatar" :src="adminForm.avatar" class="avatar-preview">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="upload-tip">建议尺寸：100x100像素，支持jpg/png格式，不超过2MB</div>
            </el-form-item>
            
            <el-form-item label="创建时间">
              <el-input :value="formatDate(adminForm.createTime)" disabled />
            </el-form-item>
            
            <el-form-item label="最后登录">
              <el-input :value="formatDate(adminForm.lastLoginTime)" disabled />
            </el-form-item>
            
            <el-form-item v-if="editMode">
              <el-button 
                type="primary" 
                @click="saveProfile"
                :loading="saveLoading"
              >
                {{ saveLoading ? '保存中...' : '保存信息' }}
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 修改密码卡片 -->
      <el-col :span="12">
        <el-card class="password-card">
          <div slot="header" class="card-header">
            <span>修改密码</span>
          </div>
          
          <el-form 
            :model="passwordForm" 
            :rules="passwordRules" 
            ref="passwordForm" 
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="changePassword"
                :loading="passwordLoading"
              >
                {{ passwordLoading ? '修改中...' : '修改密码' }}
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作卡片 -->
    <el-card class="quick-actions-card" style="margin-top: 20px;">
      <div slot="header" class="card-header">
        <span>快捷操作</span>
      </div>

      <div class="quick-actions">
        <el-button
          type="primary"
          icon="el-icon-document"
          @click="$router.push('/admin/operation-log')"
        >
          查看操作记录
        </el-button>
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="$router.push('/admin/software')"
        >
          添加软件
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-setting"
          @click="$router.push('/admin/site-config')"
        >
          网站配置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminApi } from '@/api'
import { storage } from '@/utils'

export default {
  name: 'AdminProfile',
  data() {
    return {
      editMode: false,
      saveLoading: false,
      passwordLoading: false,
      uploadUrl: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8088/api',
      
      adminForm: {
        id: null,
        username: '',
        nickname: '',
        email: '',
        phone: '',
        avatar: '',
        createTime: null,
        lastLoginTime: null
      },
      
      adminRules: {
        nickname: [
          { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      },
      

    }
  },
  
  created() {
    this.loadAdminInfo()
  },
  
  methods: {
    // 加载管理员信息
    loadAdminInfo() {
      const admin = storage.get('admin')
      if (admin) {
        this.adminForm = {
          id: admin.id,
          username: admin.username,
          nickname: admin.nickname || '',
          email: admin.email || '',
          phone: admin.phone || '',
          avatar: admin.avatar || '',
          createTime: admin.createTime,
          lastLoginTime: admin.lastLoginTime
        }
      }
    },
    
    // 保存个人信息
    async saveProfile() {
      this.$refs.adminForm.validate(async (valid) => {
        if (!valid) return
        
        this.saveLoading = true
        try {
          await adminApi.updateProfile(this.adminForm)
          
          // 更新本地存储的管理员信息
          const admin = storage.get('admin')
          const updatedAdmin = { ...admin, ...this.adminForm }
          storage.set('admin', updatedAdmin)
          
          this.$message.success('个人信息保存成功')
          this.editMode = false
        } catch (error) {
          console.error('保存个人信息失败:', error)
          this.$message.error('保存失败')
        } finally {
          this.saveLoading = false
        }
      })
    },
    
    // 修改密码
    async changePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (!valid) return
        
        this.passwordLoading = true
        try {
          await adminApi.changePassword({
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword
          })
          
          this.$message.success('密码修改成功，请重新登录')
          this.resetPasswordForm()
          
          // 清除登录信息，跳转到登录页
          setTimeout(() => {
            storage.remove('admin')
            this.$router.push('/admin/login')
          }, 1500)
        } catch (error) {
          console.error('修改密码失败:', error)
          this.$message.error(error.response?.data?.message || '修改密码失败')
        } finally {
          this.passwordLoading = false
        }
      })
    },
    
    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    
    // 头像上传成功处理
    handleAvatarSuccess(response) {
      if (response.code === 200) {
        let avatarUrl = response.data.url
        if (avatarUrl && !avatarUrl.startsWith('http')) {
          avatarUrl = 'http://localhost:8088' + avatarUrl
        }
        this.adminForm.avatar = avatarUrl
        this.$message.success('头像上传成功')
      } else {
        this.$message.error(response.message || '头像上传失败')
      }
    },
    
    // 头像上传前验证
    beforeAvatarUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    // 重置表单
    resetForm() {
      this.loadAdminInfo()
      this.$refs.adminForm.clearValidate()
    },
    
    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$nextTick(() => {
        this.$refs.passwordForm && this.$refs.passwordForm.clearValidate()
      })
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '暂无记录'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },


  }
}
</script>

<style scoped>
.admin-profile {
  background: white;
  padding: 0;
  border-radius: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

/* 表单文字大小优化 */
.admin-profile .el-form-item__label {
  font-size: 14px !important;
  font-weight: 500;
}

.admin-profile .el-input__inner,
.admin-profile .el-textarea__inner {
  font-size: 14px;
}

.admin-profile .el-button {
  font-size: 14px;
}

.profile-card,
.password-card,
.log-card {
  margin-bottom: 0;
  border-radius: 0;
  border: none;
  box-shadow: none;
  border-bottom: 1px solid #e6e6e6;
}

.profile-card .el-card__header,
.password-card .el-card__header,
.quick-actions-card .el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
}

.profile-card .el-card__body,
.password-card .el-card__body,
.quick-actions-card .el-card__body {
  padding: 20px;
}

.form-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

/* 头像上传样式 */
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
  border-radius: 6px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

/* 快捷操作样式 */
.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-profile {
    padding: 15px;
  }
  
  .el-col {
    margin-bottom: 20px;
  }
}
</style>