// 模拟数据 - 用于前端独立运行
export const mockSoftwareList = [
  {
    id: 1,
    name: "Visual Studio Code",
    categoryId: 2,
    icon: "/logo.svg",
    description: "轻量级但功能强大的源代码编辑器",
    detailDescription: "Visual Studio Code是微软开发的一款免费、开源的代码编辑器，支持多种编程语言，具有智能代码补全、语法高亮、代码调试等功能。",
    publisher: "Microsoft",
    publishTime: "2024-01-15 10:30:00",
    tags: "编辑器,开发工具,免费",
    screenshots: "/logo.svg,/logo.svg",
    features: "• 智能代码补全\n• 内置Git支持\n• 丰富的扩展生态\n• 多语言支持",
    changelog: "v1.85.0\n• 新增AI代码助手\n• 优化性能\n• 修复已知问题",
    downloadUrl: "https://code.visualstudio.com/",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 1250,
    status: 1,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "IntelliJ IDEA",
    categoryId: 2,
    icon: "/logo.svg",
    description: "智能的Java集成开发环境",
    detailDescription: "IntelliJ IDEA是JetBrains开发的Java集成开发环境，被广泛认为是最智能的Java IDE之一。",
    publisher: "JetBrains",
    publishTime: "2024-01-10 14:20:00",
    tags: "IDE,Java,开发工具",
    screenshots: "/logo.svg",
    features: "• 智能代码分析\n• 强大的重构功能\n• 版本控制集成\n• 数据库工具",
    changelog: "v2024.1\n• 新增AI助手\n• 改进代码补全\n• 性能优化",
    downloadUrl: "https://www.jetbrains.com/idea/",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 980,
    status: 1,
    createTime: "2024-01-10 14:20:00",
    updateTime: "2024-01-10 14:20:00"
  },
  {
    id: 3,
    name: "Chrome浏览器",
    categoryId: 5,
    icon: "/logo.svg",
    description: "快速、安全的网络浏览器",
    detailDescription: "Google Chrome是由Google开发的免费网页浏览器，以速度和安全性著称。",
    publisher: "Google",
    publishTime: "2024-01-08 09:15:00",
    tags: "浏览器,网络工具,免费",
    screenshots: "/logo.svg,/logo.svg",
    features: "• 快速浏览\n• 安全防护\n• 扩展支持\n• 同步功能",
    changelog: "v120.0\n• 改进安全性\n• 新增功能\n• 性能提升",
    downloadUrl: "https://www.google.com/chrome/",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 2100,
    status: 1,
    createTime: "2024-01-08 09:15:00",
    updateTime: "2024-01-08 09:15:00"
  },
  {
    id: 4,
    name: "Photoshop",
    categoryId: 6,
    icon: "/logo.svg",
    description: "专业的图像编辑软件",
    detailDescription: "Adobe Photoshop是世界上最著名的图像编辑和处理软件，广泛用于平面设计、网页设计、摄影等领域。",
    publisher: "Adobe",
    publishTime: "2024-01-05 16:45:00",
    tags: "图像编辑,设计工具,专业软件",
    screenshots: "/logo.svg",
    features: "• 强大的图像编辑\n• 丰富的滤镜效果\n• 图层管理\n• 批处理功能",
    changelog: "v2024\n• AI功能增强\n• 新增工具\n• 界面优化",
    downloadUrl: "https://www.adobe.com/products/photoshop.html",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 1680,
    status: 1,
    createTime: "2024-01-05 16:45:00",
    updateTime: "2024-01-05 16:45:00"
  },
  {
    id: 5,
    name: "WinRAR",
    categoryId: 8,
    icon: "/logo.svg",
    description: "强大的压缩解压工具",
    detailDescription: "WinRAR是一款功能强大的压缩文件管理器，支持多种压缩格式。",
    publisher: "RARLAB",
    publishTime: "2024-01-03 11:30:00",
    tags: "压缩工具,系统工具,实用工具",
    screenshots: "/logo.svg,/logo.svg",
    features: "• 多格式支持\n• 高压缩比\n• 密码保护\n• 批量操作",
    changelog: "v6.24\n• 支持新格式\n• 性能改进\n• 界面更新",
    downloadUrl: "https://www.win-rar.com/",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 1420,
    status: 1,
    createTime: "2024-01-03 11:30:00",
    updateTime: "2024-01-03 11:30:00"
  },
  {
    id: 6,
    name: "Office 2024",
    categoryId: 10,
    icon: "/logo.svg",
    description: "微软办公软件套装",
    detailDescription: "Microsoft Office是微软开发的办公软件套装，包含Word、Excel、PowerPoint等应用。",
    publisher: "Microsoft",
    publishTime: "2024-01-01 08:00:00",
    tags: "办公软件,文档处理,表格制作",
    screenshots: "/logo.svg",
    features: "• Word文档处理\n• Excel表格制作\n• PowerPoint演示\n• 云端同步",
    changelog: "v2024\n• AI功能集成\n• 协作增强\n• 性能优化",
    downloadUrl: "https://www.microsoft.com/office/",
    filePath: null,
    fileName: null,
    fileSize: null,
    downloadCount: 3200,
    status: 1,
    createTime: "2024-01-01 08:00:00",
    updateTime: "2024-01-01 08:00:00"
  }
]

export const mockCategoryList = [
  {
    id: 1,
    name: "开发工具",
    description: "编程开发相关工具",
    icon: null,
    parentId: null,
    sortOrder: 1,
    status: 1,
    children: [
      {
        id: 2,
        name: "代码编辑器",
        description: "各种代码编辑器和IDE",
        icon: null,
        parentId: 1,
        sortOrder: 1,
        status: 1
      },
      {
        id: 3,
        name: "版本控制",
        description: "Git等版本控制工具",
        icon: null,
        parentId: 1,
        sortOrder: 2,
        status: 1
      }
    ]
  },
  {
    id: 4,
    name: "网络工具",
    description: "网络相关工具软件",
    icon: null,
    parentId: null,
    sortOrder: 2,
    status: 1,
    children: [
      {
        id: 5,
        name: "浏览器",
        description: "各种网页浏览器",
        icon: null,
        parentId: 4,
        sortOrder: 1,
        status: 1
      }
    ]
  },
  {
    id: 6,
    name: "图像处理",
    description: "图像编辑和处理工具",
    icon: null,
    parentId: null,
    sortOrder: 3,
    status: 1,
    children: [
      {
        id: 7,
        name: "图像编辑",
        description: "专业图像编辑软件",
        icon: null,
        parentId: 6,
        sortOrder: 1,
        status: 1
      }
    ]
  },
  {
    id: 8,
    name: "系统工具",
    description: "系统维护和优化工具",
    icon: null,
    parentId: null,
    sortOrder: 4,
    status: 1,
    children: [
      {
        id: 9,
        name: "压缩工具",
        description: "文件压缩解压工具",
        icon: null,
        parentId: 8,
        sortOrder: 1,
        status: 1
      }
    ]
  },
  {
    id: 10,
    name: "办公软件",
    description: "办公和文档处理软件",
    icon: null,
    parentId: null,
    sortOrder: 5,
    status: 1,
    children: [
      {
        id: 11,
        name: "文档处理",
        description: "文档编辑和处理工具",
        icon: null,
        parentId: 10,
        sortOrder: 1,
        status: 1
      }
    ]
  }
]

export const mockSiteConfig = {
  siteName: "软件下载之家",
  siteLogo: "/logo.svg",
  siteDescription: "专业的软件下载站点",
  siteKeywords: "软件下载,免费软件,绿色软件,系统工具"
}
