<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7b6223c9-0237-4f3e-a261-80c6c6eaa41b" name="更改" comment="add:页面修改完成">
      <change afterPath="$PROJECT_DIR$/../.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.idea/软件下载网站.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/styles/SoftwareList.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/SoftwareDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/SoftwareDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/SoftwareList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/SoftwareList.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30dFxDkmZbJfobHnKJbEs8IBXh9" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/Users/<USER>/test/软件下载网站/frontend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "configurable.group.appearance",
    "ts.external.directory.path": "D:\\Software\\dev\\JetBrains\\WebStorm 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7b6223c9-0237-4f3e-a261-80c6c6eaa41b" name="更改" comment="" />
      <created>1753949786959</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753949786959</updated>
      <workItem from="1753949788578" duration="68000" />
      <workItem from="1753950175124" duration="7949000" />
    </task>
    <task id="LOCAL-00001" summary="add:页面修改完成">
      <option name="closed" value="true" />
      <created>1753967988400</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753967988400</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add:页面修改完成" />
    <option name="LAST_COMMIT_MESSAGE" value="add:页面修改完成" />
  </component>
</project>