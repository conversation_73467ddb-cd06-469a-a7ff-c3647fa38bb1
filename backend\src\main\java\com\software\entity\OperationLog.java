package com.software.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作记录实体类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class OperationLog {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 管理员ID
     */
    private Long adminId;
    
    /**
     * 管理员用户名
     */
    private String adminUsername;
    
    /**
     * 操作类型：ADD-新增，UPDATE-修改，DELETE-删除，LOGIN-登录，LOGOUT-登出
     */
    private String operationType;
    
    /**
     * 操作模块：SOFTWARE-软件管理，CATEGORY-分类管理，SITE_CONFIG-网站配置，PROFILE-个人信息
     */
    private String operationModule;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 操作目标ID（如软件ID、分类ID等）
     */
    private Long targetId;
    
    /**
     * 操作目标名称
     */
    private String targetName;
    
    /**
     * 请求方法：GET、POST、PUT、DELETE
     */
    private String requestMethod;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 操作状态：1-成功，0-失败
     */
    private Integer operationStatus;
    
    /**
     * 错误信息（操作失败时记录）
     */
    private String errorMessage;
    
    /**
     * 执行时间（毫秒）
     */
    private Integer executionTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    // 操作类型常量
    public static final String OPERATION_TYPE_ADD = "ADD";
    public static final String OPERATION_TYPE_UPDATE = "UPDATE";
    public static final String OPERATION_TYPE_DELETE = "DELETE";
    public static final String OPERATION_TYPE_LOGIN = "LOGIN";
    public static final String OPERATION_TYPE_LOGOUT = "LOGOUT";
    
    // 操作模块常量
    public static final String OPERATION_MODULE_SOFTWARE = "SOFTWARE";
    public static final String OPERATION_MODULE_CATEGORY = "CATEGORY";
    public static final String OPERATION_MODULE_SITE_CONFIG = "SITE_CONFIG";
    public static final String OPERATION_MODULE_PROFILE = "PROFILE";
    
    // 操作状态常量
    public static final Integer OPERATION_STATUS_SUCCESS = 1;
    public static final Integer OPERATION_STATUS_FAILED = 0;
}
