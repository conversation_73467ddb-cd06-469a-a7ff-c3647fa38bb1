<template>
  <div class="software-detail">
    <!-- 网站头部 -->
    <div class="site-header">
      <div class="container">
        <div class="header-top">
          <div class="logo">
            <img v-if="siteConfig.siteLogo" :src="siteConfig.siteLogo" :alt="siteConfig.siteName" />
            <img v-else src="/logo.svg" alt="软件下载网站" />
            <span>{{ siteConfig.siteName || '软件下载之家' }}</span>
          </div>
          <div class="search-area">
            <el-input
              v-model="searchKeyword"
              placeholder="想下载什么软件？"
              @keyup.enter="handleSearch"
              clearable
              class="search-input"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="main-nav">
      <div class="container">
        <ul class="nav-list">
          <li class="nav-item" :class="{ active: activeCategory === null }">
            <a href="#" @click.prevent="goToHome">网站首页</a>
          </li>
          <li
            v-for="category in categoryList"
            :key="category.id"
            class="nav-item dropdown"
            :class="{ active: isParentCategoryActive(category.id) }"
            @mouseenter="showDropdown = category.id"
            @mouseleave="showDropdown = null"
          >
            <a href="#" @click.prevent="goToCategoryPage(category.id)" class="dropdown-toggle">
              {{ category.name }}
              <i class="el-icon-arrow-down" v-if="category.children && category.children.length > 0"></i>
            </a>
            <!-- 二级分类下拉菜单 -->
            <ul
              v-if="category.children && category.children.length > 0"
              class="dropdown-menu"
              :class="{ show: showDropdown === category.id }"
            >
              <li v-for="subCategory in category.children" :key="subCategory.id">
                <a
                  href="#"
                  @click.prevent="goToCategoryPage(subCategory.id)"
                  :class="{ active: activeCategory === subCategory.id }"
                >
                  {{ subCategory.name }}
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <el-button
              type="text"
              icon="el-icon-setting"
              @click="goToAdmin"
              class="admin-link"
            >
              后台管理
            </el-button>
          </li>
        </ul>
      </div>
    </div>

    <div class="main-content">
      <div class="container" v-loading="loading">
        <!-- 返回按钮 -->
        <div class="back-btn">
          <el-button @click="$router.go(-1)" icon="el-icon-arrow-left">返回</el-button>
        </div>

        <div v-if="software" class="detail-content">
          <!-- 软件头部信息 -->
          <div class="software-header">
            <div class="software-icon">
              <img :src="software.icon" :alt="software.name" />
            </div>
            <div class="software-info">
              <h1 class="software-name">{{ software.name }}</h1>
              <div class="software-meta">
                <div class="meta-item">
                  <span class="label">发布人:</span>
                  <span class="value">{{ software.publisher }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">发布时间:</span>
                  <span class="value">{{ formatDate(software.publishTime) }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">下载次数:</span>
                  <span class="value">{{ software.downloadCount }}</span>
                </div>
              </div>
              <div class="software-tags">
                <el-tag
                  v-for="tag in parseTags(software.tags)"
                  :key="tag"
                  type="primary"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 软件详细信息 -->
          <div class="detail-sections">
            <!-- 软件介绍 -->
            <div class="section">
              <h2 class="section-title">软件介绍</h2>
              <div class="section-content">
                <p class="description">{{ software.detailDescription || software.description }}</p>
              </div>
            </div>

            <!-- 软件截图 -->
            <div v-if="screenshots.length > 0" class="section">
              <h2 class="section-title">软件截图</h2>
              <div class="section-content">
                <div class="screenshots">
                  <div
                    v-for="(screenshot, index) in screenshots"
                    :key="index"
                    class="screenshot-item"
                    @click="previewImage(screenshot)"
                  >
                    <img :src="screenshot" :alt="`截图${index + 1}`" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 软件功能 -->
            <div v-if="software.features && software.features.trim()" class="section">
              <h2 class="section-title">软件功能</h2>
              <div class="section-content">
                <div class="features-text">
                  <pre class="features-content">{{ software.features }}</pre>
                </div>
              </div>
            </div>

            <!-- 更新日志 -->
            <div v-if="software.changelog && software.changelog.trim()" class="section">
              <h2 class="section-title">更新日志</h2>
              <div class="section-content">
                <div class="changelog">
                  <pre>{{ software.changelog }}</pre>
                </div>
              </div>
            </div>

            <!-- 下载信息 -->
            <div class="section">
              <h2 class="section-title">下载信息</h2>
              <div class="section-content">
                <div class="download-options">
                  <!-- 本地下载信息 -->
                  <div class="download-option">
                    <h3 class="option-title">
                      <i class="el-icon-download"></i>
                      本地下载
                    </h3>
                    <div class="option-content">
                      <div v-if="software.filePath" class="file-info">
                        <p><strong>文件名:</strong> {{ software.fileName || software.name }}</p>
                        <p v-if="software.fileSize"><strong>文件大小:</strong> {{ formatFileSize(software.fileSize) }}</p>
                        <p class="file-desc">直接下载服务器上的软件文件，速度快，无需跳转</p>
                        <el-button
                          type="primary"
                          size="large"
                          @click="handleLocalDownload"
                          icon="el-icon-download"
                          class="download-btn"
                        >
                          本地下载
                        </el-button>
                      </div>
                      <div v-else class="no-file">
                        <p>暂无本地下载文件</p>
                      </div>
                    </div>
                  </div>

                  <!-- 网盘下载信息 -->
                  <div class="download-option">
                    <h3 class="option-title">
                      <i class="el-icon-link"></i>
                      网盘下载
                    </h3>
                    <div class="option-content">
                      <div v-if="software.downloadUrl" class="network-info">
                        <el-input
                          :value="software.downloadUrl"
                          readonly
                          class="download-url"
                        >
                          <el-button
                            slot="append"
                            @click="copyDownloadUrl"
                            icon="el-icon-copy-document"
                          >
                            复制
                          </el-button>
                        </el-input>
                        <p class="url-desc">跳转到网盘或第三方下载站点</p>
                        <el-button
                          type="success"
                          size="large"
                          @click="handleNetworkDownload"
                          icon="el-icon-link"
                          class="download-btn"
                        >
                          网盘下载
                        </el-button>
                      </div>
                      <div v-else class="no-url">
                        <p>暂无网盘下载地址</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !software" class="empty-state">
          <el-empty description="软件不存在或已下架"></el-empty>
        </div>
      </div>
    </div>

    <!-- 网站底部 -->
    <footer class="site-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <p class="site-desc">软件下载之家 - 专业的软件下载站点</p>
            <p class="site-keywords">提供各类软件下载，包括开发工具、办公软件、图像处理、系统工具等</p>
            <div class="footer-links">
              <div class="contact-email">
                <i class="el-icon-message"></i>
                <span>联系邮箱: <EMAIL></span>
              </div>
              <div class="icp-info">
                <i class="el-icon-document"></i>
                <span>备案号: 京ICP备12345678号</span>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 软件下载之家. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- 全屏图片预览 -->
    <div v-if="previewVisible" class="fullscreen-image-preview" @click="closePreview">
      <div class="preview-controls-bottom">
        <div class="control-buttons">
          <button @click.stop="zoomOut" class="control-btn" title="缩小">
            <i class="el-icon-zoom-out"></i>
          </button>
          <button @click.stop="zoomIn" class="control-btn" title="放大">
            <i class="el-icon-zoom-in"></i>
          </button>
          <button @click.stop="resetZoom" class="control-btn" title="重置">
            <i class="el-icon-refresh"></i>
          </button>
          <button @click.stop="rotateLeft" class="control-btn" title="左转">
            <i class="el-icon-refresh-left"></i>
          </button>
          <button @click.stop="rotateRight" class="control-btn" title="右转">
            <i class="el-icon-refresh-right"></i>
          </button>
          <button @click.stop="closePreview" class="control-btn close-btn" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>
      <div class="fullscreen-image-container" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag" @mouseleave="endDrag" @wheel="onWheel">
        <img
          :src="previewImageUrl"
          class="fullscreen-preview-image"
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
        />
      </div>
    </div>
  </div>
</template>

<script>
import { softwareApi } from '@/api'
import { formatDate, parseTags, parseScreenshots } from '@/utils'

export default {
  name: 'SoftwareDetail',
  data() {
    return {
      software: null,
      loading: false,
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      searchKeyword: '',
      categoryList: [],
      showDropdown: null,
      activeCategory: null,
      siteConfig: {} // 网站配置
    }
  },
  computed: {
    screenshots() {
      return parseScreenshots(this.software?.screenshots)
    },

    // 图片样式
    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    }
  },
  created() {
    this.loadSoftwareDetail()
    this.loadCategoryList()
    this.loadSiteConfig()
  },
  methods: {
    formatDate,
    parseTags,

    async loadSoftwareDetail() {
      const id = this.$route.params.id
      if (!id) return

      this.loading = true
      try {
        const res = await softwareApi.getSoftwareDetail(id)
        // 处理图标和截图URL
        this.software = {
          ...res.data,
          icon: res.data.icon && !res.data.icon.startsWith('http')
            ? `http://localhost:8088${res.data.icon}`
            : res.data.icon,
          screenshots: res.data.screenshots ?
            res.data.screenshots.split(',').map(url =>
              url.trim() && !url.trim().startsWith('http')
                ? `http://localhost:8088${url.trim()}`
                : url.trim()
            ).filter(Boolean).join(',')
            : ''
        }
      } catch (error) {
        console.error('加载软件详情失败:', error)
        // 使用模拟数据
        const mockSoftware = mockSoftwareList.find(s => s.id == id)
        if (mockSoftware) {
          this.software = mockSoftware
        }
      } finally {
        this.loading = false
      }
    },

    // 本地下载 - 下载上传的附件文件
    async handleLocalDownload() {
      if (!this.software.filePath) {
        this.$message.warning('该软件暂无本地下载文件')
        return
      }

      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++

        // 构建本地文件下载URL
        let downloadUrl = this.software.filePath
        if (!downloadUrl.startsWith('http')) {
          downloadUrl = `http://localhost:8088${this.software.filePath}`
        }

        // 创建隐藏的下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = this.software.fileName || this.software.name
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('开始本地下载')
      } catch (error) {
        console.error('本地下载失败:', error)
        this.$message.error('本地下载失败')
      }
    },

    // 网盘下载 - 跳转到管理后台设置的下载地址
    async handleNetworkDownload() {
      if (!this.software.downloadUrl) {
        this.$message.warning('该软件暂无网盘下载地址')
        return
      }

      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++
        window.open(this.software.downloadUrl, '_blank')
        this.$message.success('正在跳转到网盘下载')
      } catch (error) {
        console.error('网盘下载失败:', error)
        this.$message.error('网盘下载失败')
      }
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
      // 重置图片状态
      this.resetImageState()
    },

    // 重置图片状态
    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    // 放大图片
    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    // 缩小图片
    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    // 重置缩放
    resetZoom() {
      this.resetImageState()
    },

    // 左旋转
    rotateLeft() {
      this.imageRotation -= 90
    },

    // 右旋转
    rotateRight() {
      this.imageRotation += 90
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    // 拖拽中
    onDrag(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
      }
    },

    // 结束拖拽
    endDrag() {
      this.isDragging = false
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.resetImageState()
    },

    // 鼠标滚轮缩放
    onWheel(event) {
      event.preventDefault()
      const delta = event.deltaY
      if (delta < 0) {
        // 向上滚动，放大
        this.zoomIn()
      } else {
        // 向下滚动，缩小
        this.zoomOut()
      }
    },

    copyDownloadUrl() {
      navigator.clipboard.writeText(this.software.downloadUrl).then(() => {
        this.$message.success('下载地址已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 搜索功能
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$router.push({
          path: '/',
          query: { search: this.searchKeyword.trim() }
        })
      }
    },

    // 加载分类列表
    async loadCategoryList() {
      try {
        const res = await softwareApi.getCategoryList()
        this.categoryList = res.data || []
      } catch (error) {
        console.error('加载分类失败:', error)
        // 使用模拟数据
        this.categoryList = mockCategoryList
      }
    },

    // 导航栏相关方法
    // 跳转到首页
    goToHome() {
      this.$router.push('/')
    },

    // 跳转到后台管理
    goToAdmin() {
      this.$router.push('/admin/login')
    },

    // 跳转到分类页面
    goToCategoryPage(categoryId) {
      this.$router.push({
        path: '/',
        query: { category: categoryId }
      })
    },

    // 判断父分类是否处于活动状态
    isParentCategoryActive(parentId) {
      if (this.activeCategory === parentId) return true

      // 检查是否有子分类处于活动状态
      const parentCategory = this.categoryList.find(c => c.id === parentId)
      if (parentCategory && parentCategory.children) {
        return parentCategory.children.some(child => child.id === this.activeCategory)
      }

      return false
    },

    // 搜索功能
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$router.push({
          path: '/',
          query: { search: this.searchKeyword.trim() }
        })
      }
    },

    // 加载网站配置
    async loadSiteConfig() {
      try {
        const response = await softwareApi.getSiteConfig()
        this.siteConfig = response.data || {}
      } catch (error) {
        console.error('加载网站配置失败:', error)
        // 使用模拟数据
        this.siteConfig = mockSiteConfig
      }
    }
  }
}
</script>

<style scoped>
.software-detail {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 网站头部 */
.site-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 15px 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.logo img {
  width: 40px;
  height: 40px;
}

.search-area {
  flex: 1;
  max-width: 500px;
  margin: 0 50px;
}

.search-input {
  width: 100%;
}

/* 主导航 */
.main-nav {
  background: #4a90e2;
  border-bottom: 1px solid #357abd;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav-item:hover a,
.nav-item.active a {
  background: rgba(255, 255, 255, 0.1);
}

.admin-link {
  color: #fff !important;
  padding: 15px 20px !important;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-toggle i {
  font-size: 12px;
  transition: transform 0.3s;
}

.dropdown:hover .dropdown-toggle i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
  padding: 8px 0;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu a {
  display: block;
  padding: 8px 16px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  background: #f5f5f5;
  color: #4a90e2;
}

/* 主导航 */
.main-nav {
  background: #4a90e2;
  border-bottom: 1px solid #357abd;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav-item:hover a,
.nav-item.active a {
  background: rgba(255, 255, 255, 0.1);
}

.admin-link {
  color: #fff !important;
  padding: 15px 20px !important;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-toggle i {
  font-size: 12px;
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
  padding: 8px 0;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu a {
  display: block;
  padding: 8px 16px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  background: #f5f5f5;
  color: #4a90e2;
}

/* 主内容 */
.main-content {
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.back-btn {
  margin-bottom: 1.5rem;
}

.detail-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.software-header {
  display: flex;
  padding: 2rem;
  border-bottom: 1px solid #eee;
  gap: 2rem;
}

.software-icon img {
  width: 128px;
  height: 128px;
  border-radius: 12px;
  object-fit: cover;
}

.software-info {
  flex: 1;
}

.software-name {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.software-meta {
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  margin-bottom: 0.5rem;
}

.meta-item .label {
  width: 80px;
  color: #666;
  font-weight: 500;
}

.meta-item .value {
  color: #333;
}

.software-tags {
  margin-bottom: 1.5rem;
}

.software-tags .el-tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.detail-sections {
  padding: 0 2rem 2rem;
}

.section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #409eff;
}

.section-content {
  color: #666;
  line-height: 1.6;
}

.description {
  font-size: 1rem;
  white-space: pre-wrap;
}

.screenshots {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.screenshot-item {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.screenshot-item:hover {
  transform: scale(1.03);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.screenshot-item img {
  width: 100%;
  height: auto;
  min-height: 300px;
  max-height: 500px;
  object-fit: contain;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
}

.features-text {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.features-content {
  font-family: inherit;
  white-space: pre-wrap;
  margin: 0;
  color: #333;
  line-height: 1.6;
}

.changelog {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.changelog pre {
  font-family: inherit;
  white-space: pre-wrap;
  margin: 0;
}

/* 下载选项样式 */
.download-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.download-option {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafafa;
}

.option-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-title i {
  color: #409eff;
}

.option-content {
  color: #666;
}

.file-info p, .network-info p {
  margin: 0.5rem 0;
}

.file-desc, .url-desc {
  font-size: 0.9rem;
  color: #999;
  margin-top: 0.5rem;
}

.no-file, .no-url {
  color: #999;
  font-style: italic;
}

.download-url {
  margin-bottom: 0.5rem;
}

.download-btn {
  margin-top: 1rem;
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
}

/* 网站底部样式 */
.site-footer {
  background: #2c3e50;
  color: #ecf0f1;
  margin-top: 40px;
  padding: 30px 0 20px;
}

.footer-content {
  margin-bottom: 20px;
}

.footer-info {
  text-align: center;
}

.site-desc {
  font-size: 16px;
  margin-bottom: 10px;
  color: #bdc3c7;
}

.site-keywords {
  font-size: 14px;
  margin-bottom: 15px;
  color: #95a5a6;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.contact-email,
.icp-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-email i,
.icp-info i {
  color: #3498db;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 15px;
  text-align: center;
}

/* 全屏图片预览样式 */
.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  cursor: pointer;
}

.fullscreen-image-container {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.preview-controls-bottom {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2001;
}

.control-buttons {
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.control-btn.close-btn {
  background: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.5);
}

.control-btn.close-btn:hover {
  background: rgba(255, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 15px;
  }

  .search-area {
    margin: 0;
    max-width: 100%;
  }

  .nav-list {
    flex-wrap: wrap;
  }

  .nav-item a {
    padding: 12px 15px;
    font-size: 14px;
  }

  .software-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .software-icon img {
    width: 96px;
    height: 96px;
  }

  .download-options {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .logo {
    font-size: 20px;
  }

  .logo img {
    width: 32px;
    height: 32px;
  }

  .nav-item a {
    padding: 10px 12px;
    font-size: 13px;
  }

  .software-name {
    font-size: 1.5rem;
  }

  .detail-sections {
    padding: 0 1rem 1rem;
  }
}
</style>
