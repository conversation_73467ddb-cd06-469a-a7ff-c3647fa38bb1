<template>
  <div class="software-detail">
    <div class="container" v-loading="loading">
      <!-- 返回按钮 -->
      <div class="back-btn">
        <el-button @click="$router.go(-1)" icon="el-icon-arrow-left">返回</el-button>
      </div>

      <div v-if="software" class="detail-content">
        <!-- 软件头部信息 -->
        <div class="software-header">
          <div class="software-icon">
            <img :src="software.icon" :alt="software.name" />
          </div>
          <div class="software-info">
            <h1 class="software-name">{{ software.name }}</h1>
            <div class="software-meta">
              <div class="meta-item">
                <span class="label">发布人:</span>
                <span class="value">{{ software.publisher }}</span>
              </div>
              <div class="meta-item">
                <span class="label">发布时间:</span>
                <span class="value">{{ formatDate(software.publishTime) }}</span>
              </div>
              <div class="meta-item">
                <span class="label">下载次数:</span>
                <span class="value">{{ software.downloadCount }}</span>
              </div>
            </div>
            <div class="software-tags">
              <el-tag 
                v-for="tag in parseTags(software.tags)" 
                :key="tag"
                type="primary"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 软件详细信息 -->
        <div class="detail-sections">
          <!-- 软件介绍 -->
          <div class="section">
            <h2 class="section-title">软件介绍</h2>
            <div class="section-content">
              <p class="description">{{ software.detailDescription || software.description }}</p>
            </div>
          </div>

          <!-- 软件截图 -->
          <div v-if="screenshots.length > 0" class="section">
            <h2 class="section-title">软件截图</h2>
            <div class="section-content">
              <div class="screenshots">
                <div 
                  v-for="(screenshot, index) in screenshots" 
                  :key="index"
                  class="screenshot-item"
                  @click="previewImage(screenshot)"
                >
                  <img :src="screenshot" :alt="`截图${index + 1}`" />
                </div>
              </div>
            </div>
          </div>

          <!-- 软件功能 -->
          <div v-if="software.features" class="section">
            <h2 class="section-title">软件功能</h2>
            <div class="section-content">
              <div class="features-text">
                <pre class="features-content">{{ software.features }}</pre>
              </div>
            </div>
          </div>

          <!-- 更新日志 -->
          <div v-if="software.changelog" class="section">
            <h2 class="section-title">更新日志</h2>
            <div class="section-content">
              <div class="changelog">
                <pre>{{ software.changelog }}</pre>
              </div>
            </div>
          </div>

          <!-- 下载信息 -->
          <div class="section">
            <h2 class="section-title">下载信息</h2>
            <div class="section-content">
              <div class="download-options">
                <!-- 本地下载信息 -->
                <div class="download-option">
                  <h3 class="option-title">
                    <i class="el-icon-download"></i>
                    本地下载
                  </h3>
                  <div class="option-content">
                    <div v-if="software.filePath" class="file-info">
                      <p><strong>文件名:</strong> {{ software.fileName || software.name }}</p>
                      <p v-if="software.fileSize"><strong>文件大小:</strong> {{ formatFileSize(software.fileSize) }}</p>
                      <p class="file-desc">直接下载服务器上的软件文件，速度快，无需跳转</p>
                      <el-button 
                        type="primary" 
                        size="large"
                        @click="handleLocalDownload"
                        icon="el-icon-download"
                        class="download-btn"
                      >
                        本地下载
                      </el-button>
                    </div>
                    <div v-else class="no-file">
                      <p>暂无本地下载文件</p>
                    </div>
                  </div>
                </div>
                
                <!-- 网盘下载信息 -->
                <div class="download-option">
                  <h3 class="option-title">
                    <i class="el-icon-link"></i>
                    网盘下载
                  </h3>
                  <div class="option-content">
                    <div v-if="software.downloadUrl" class="network-info">
                      <el-input 
                        :value="software.downloadUrl" 
                        readonly
                        class="download-url"
                      >
                        <el-button 
                          slot="append" 
                          @click="copyDownloadUrl"
                          icon="el-icon-copy-document"
                        >
                          复制
                        </el-button>
                      </el-input>
                      <p class="url-desc">跳转到网盘或第三方下载站点</p>
                      <el-button 
                        type="success" 
                        size="large"
                        @click="handleNetworkDownload"
                        icon="el-icon-link"
                        class="download-btn"
                      >
                        网盘下载
                      </el-button>
                    </div>
                    <div v-else class="no-url">
                      <p>暂无网盘下载地址</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && !software" class="empty-state">
        <el-empty description="软件不存在或已下架"></el-empty>
      </div>
    </div>

    <!-- 全屏图片预览 -->
    <div v-if="previewVisible" class="fullscreen-image-preview" @click="closePreview">
      <div class="preview-controls-bottom">
        <div class="control-buttons">
          <button @click.stop="zoomOut" class="control-btn" title="缩小">
            <i class="el-icon-zoom-out"></i>
          </button>
          <button @click.stop="zoomIn" class="control-btn" title="放大">
            <i class="el-icon-zoom-in"></i>
          </button>
          <button @click.stop="resetZoom" class="control-btn" title="重置">
            <i class="el-icon-refresh"></i>
          </button>
          <button @click.stop="rotateLeft" class="control-btn" title="左转">
            <i class="el-icon-refresh-left"></i>
          </button>
          <button @click.stop="rotateRight" class="control-btn" title="右转">
            <i class="el-icon-refresh-right"></i>
          </button>
          <button @click.stop="closePreview" class="control-btn close-btn" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>
      <div class="fullscreen-image-container" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag" @mouseleave="endDrag" @wheel="onWheel">
        <img 
          :src="previewImageUrl" 
          class="fullscreen-preview-image" 
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
        />
      </div>
    </div>
  </div>
</template>

<script>
import { softwareApi } from '@/api'
import { formatDate, parseTags, parseScreenshots } from '@/utils'

export default {
  name: 'SoftwareDetail',
  data() {
    return {
      software: null,
      loading: false,
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 }
    }
  },
  computed: {
    screenshots() {
      return parseScreenshots(this.software?.screenshots)
    },
    
    // 图片样式
    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    }
  },
  created() {
    this.loadSoftwareDetail()
  },
  methods: {
    formatDate,
    parseTags,
    
    async loadSoftwareDetail() {
      const id = this.$route.params.id
      if (!id) return
      
      this.loading = true
      try {
        const res = await softwareApi.getSoftwareDetail(id)
        // 处理图标和截图URL
        this.software = {
          ...res.data,
          icon: res.data.icon && !res.data.icon.startsWith('http')
            ? `http://localhost:8088${res.data.icon}`
            : res.data.icon,
          screenshots: res.data.screenshots ?
            res.data.screenshots.split(',').map(url =>
              url.trim() && !url.trim().startsWith('http')
                ? `http://localhost:8088${url.trim()}`
                : url.trim()
            ).filter(Boolean).join(',')
            : ''
        }
      } catch (error) {
        console.error('加载软件详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 本地下载 - 下载上传的附件文件
    async handleLocalDownload() {
      if (!this.software.filePath) {
        this.$message.warning('该软件暂无本地下载文件')
        return
      }
      
      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++
        
        // 构建本地文件下载URL
        let downloadUrl = this.software.filePath
        if (!downloadUrl.startsWith('http')) {
          downloadUrl = `http://localhost:8088${this.software.filePath}`
        }
        
        // 创建隐藏的下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = this.software.fileName || this.software.name
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$message.success('开始本地下载')
      } catch (error) {
        console.error('本地下载失败:', error)
        this.$message.error('本地下载失败')
      }
    },
    
    // 网盘下载 - 跳转到管理后台设置的下载地址
    async handleNetworkDownload() {
      if (!this.software.downloadUrl) {
        this.$message.warning('该软件暂无网盘下载地址')
        return
      }
      
      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++
        window.open(this.software.downloadUrl, '_blank')
        this.$message.success('正在跳转到网盘下载')
      } catch (error) {
        console.error('网盘下载失败:', error)
        this.$message.error('网盘下载失败')
      }
    },
    
    
    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
      // 重置图片状态
      this.resetImageState()
    },

    // 重置图片状态
    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    // 放大图片
    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    // 缩小图片
    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    // 重置缩放
    resetZoom() {
      this.resetImageState()
    },

    // 左旋转
    rotateLeft() {
      this.imageRotation -= 90
    },

    // 右旋转
    rotateRight() {
      this.imageRotation += 90
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    // 拖拽中
    onDrag(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
      }
    },

    // 结束拖拽
    // 结束拖拽
    endDrag() {
      this.isDragging = false
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.resetImageState()
    },

    // 鼠标滚轮缩放
    onWheel(event) {
      event.preventDefault()
      const delta = event.deltaY
      if (delta < 0) {
        // 向上滚动，放大
        this.zoomIn()
      } else {
        // 向下滚动，缩小
        this.zoomOut()
      }
    },
    
    copyDownloadUrl() {
      navigator.clipboard.writeText(this.software.downloadUrl).then(() => {
        this.$message.success('下载地址已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.software-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 2rem 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
}

.back-btn {
  margin-bottom: 1.5rem;
}

.detail-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.software-header {
  display: flex;
  padding: 2rem;
  border-bottom: 1px solid #eee;
  gap: 2rem;
}

.software-icon img {
  width: 128px;
  height: 128px;
  border-radius: 12px;
  object-fit: cover;
}

.software-info {
  flex: 1;
}

.software-name {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.software-meta {
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  margin-bottom: 0.5rem;
}

.meta-item .label {
  width: 80px;
  color: #666;
  font-weight: 500;
}

.meta-item .value {
  color: #333;
}

.software-tags {
  margin-bottom: 1.5rem;
}

.software-tags .el-tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.detail-sections {
  padding: 0 2rem 2rem;
}

.section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #409eff;
}

.section-content {
  color: #666;
  line-height: 1.6;
}

.description {
  font-size: 1rem;
  white-space: pre-wrap;
}

.screenshots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.screenshot-item {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.screenshot-item:hover {
  transform: scale(1.03);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.screenshot-item img {
  width: 100%;
  height: auto;
  min-height: 300px;
  max-height: 500px;
  object-fit: contain;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
}

.features-text {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.features-content {
  font-family: inherit;
  white-space: pre-wrap;
  margin: 0;
  color: #333;
  line-height: 1.6;
}

.features-content h1,
.features-content h2,
.features-content h3,
.features-content h4,
.features-content h5,
.features-content h6 {
  margin: 1em 0 0.5em 0;
  color: #333;
}

.features-content p {
  margin: 0.5em 0;
}

.features-content ul,
.features-content ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

.features-content li {
  margin: 0.25em 0;
}

.features-content blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #409eff;
  background: #f8f9fa;
  color: #666;
}

.features-content code {
  background: #f1f1f1;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.features-content pre {
  background: #f8f9fa;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  margin: 1em 0;
}

.features-content strong {
  font-weight: bold;
}

.features-content em {
  font-style: italic;
}

.features-content u {
  text-decoration: underline;
}

.features-content s {
  text-decoration: line-through;
}

.changelog {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.changelog pre {
  font-family: inherit;
  white-space: pre-wrap;
  margin: 0;
}

/* 下载选项样式 */
.download-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.download-option {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafafa;
}

.option-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-title i {
  color: #409eff;
}

.option-content {
  color: #666;
}

.file-info p, .network-info p {
  margin: 0.5rem 0;
}

.file-desc, .url-desc {
  font-size: 0.9rem;
  color: #999;
  margin-top: 0.5rem;
}

.no-file, .no-url {
  color: #999;
  font-style: italic;
}

.download-url {
  margin-bottom: 0.5rem;
}

.download-btn {
  margin-top: 1rem;
  width: 100%;
}

@media (max-width: 768px) {
  .download-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .download-option {
    padding: 1rem;
  }
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
}

@media (max-width: 768px) {
  .software-header {
    flex-direction: column;
    text-align: center;
  }

  .software-icon img {
    width: 96px;
    height: 96px;
  }

  .software-name {
    font-size: 1.5rem;
  }
}

/* 图片预览对话框样式 */
/* 全屏图片预览样式 */
.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.fullscreen-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  user-select: none;
}

.fullscreen-preview-image {
  max-width: 95vw;
  max-height: 95vh;
  width: auto;
  height: auto;
  object-fit: contain;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transform-origin: center center;
  cursor: grab;
}

.fullscreen-preview-image:active {
  cursor: grabbing;
}

/* 底部控制栏 */
.preview-controls-bottom {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  pointer-events: none;
}

.control-buttons {
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  padding: 15px 20px;
  border-radius: 50px;
  pointer-events: all;
}

.control-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn:active {
  transform: scale(0.95);
}

.close-btn {
  background: rgba(255, 59, 48, 0.8);
}

.close-btn:hover {
  background: rgba(255, 59, 48, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-controls-bottom {
    bottom: 20px;
  }
  
  .control-buttons {
    padding: 12px 16px;
    gap: 8px;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .fullscreen-preview-image {
    max-width: 98vw;
    max-height: 90vh;
  }
}
</style>