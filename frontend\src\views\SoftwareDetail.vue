<template>
  <div class="software-detail">
    <!-- 网站头部 -->
    <div class="site-header">
      <div class="container">
        <div class="header-top">
          <div class="logo" @click="goToHome" style="cursor: pointer;">
            <img v-if="siteConfig.siteLogo" :src="siteConfig.siteLogo" :alt="siteConfig.siteName" />
            <img v-else src="/logo.svg" alt="软件下载网站" />
            <span>{{ siteConfig.siteName || '软件下载之家' }}</span>
          </div>
          <div class="search-area">
            <el-input
              v-model="searchKeyword"
              placeholder="想下载什么软件？"
              @keyup.enter="handleSearch"
              clearable
              class="search-input"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <nav class="main-nav">
      <div class="container">
        <ul class="nav-list">
          <li class="nav-item">
            <a href="/" @click.prevent="goToHome">网站首页</a>
          </li>
          <li
            v-for="category in categoryList"
            :key="category.id"
            class="nav-item dropdown"
            :class="{ active: isParentCategoryActive(category.id) }"
            @mouseenter="showDropdown = category.id"
            @mouseleave="showDropdown = null"
          >
            <a
              href="#"
              @click.prevent="goToCategoryPage(category.id)"
              class="dropdown-toggle"
            >
              {{ category.name }}
              <i v-if="category.children && category.children.length > 0" class="el-icon-arrow-down"></i>
            </a>
            <ul
              v-if="category.children && category.children.length > 0"
              class="dropdown-menu"
              :class="{ show: showDropdown === category.id }"
            >
              <li v-for="child in category.children" :key="child.id">
                <a
                  href="#"
                  @click.prevent="goToCategoryPage(child.id)"
                  :class="{ active: activeCategory === child.id }"
                >
                  {{ child.name }}
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </nav>

    <!-- 主体内容 -->
    <div class="main-content">
      <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
          <a href="/" @click.prevent="goToHome">首页</a>
          <span> > </span>
          <span v-if="software">{{ software.name }}</span>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <el-loading text="加载中..."></el-loading>
        </div>

        <div v-if="software" class="detail-content">
          <!-- 软件头部信息 -->
          <div class="software-header">
            <div class="software-icon">
              <img :src="software.icon" :alt="software.name" />
            </div>
            <div class="software-info">
              <h1 class="software-name">{{ software.name }}</h1>
              <div class="software-meta">
                <div class="meta-item">
                  <span class="label">发布人:</span>
                  <span class="value">{{ software.publisher }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">发布时间:</span>
                  <span class="value">{{ formatDate(software.publishTime) }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">下载次数:</span>
                  <span class="value">{{ software.downloadCount }}</span>
                </div>
              </div>
              <div class="software-tags">
                <el-tag
                  v-for="tag in parseTags(software.tags)"
                  :key="tag"
                  type="primary"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 软件详细信息 -->
          <div class="detail-sections">
            <!-- 软件介绍 -->
            <div class="section">
              <h2 class="section-title">软件介绍</h2>
              <div class="section-content">
                <p class="description">{{ software.detailDescription || software.description }}</p>
              </div>
            </div>

            <!-- 软件截图 -->
            <div v-if="screenshots.length > 0" class="section">
              <h2 class="section-title">软件截图</h2>
              <div class="section-content">
                <div class="screenshots">
                  <div
                    v-for="(screenshot, index) in screenshots"
                    :key="index"
                    class="screenshot-item"
                    @click="previewImage(screenshot)"
                  >
                    <img :src="screenshot" :alt="`截图${index + 1}`" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 软件功能 -->
            <div v-if="software.features && software.features.trim()" class="section">
              <h2 class="section-title">软件功能</h2>
              <div class="section-content">
                <div class="features-text">
                  <pre class="features-content">{{ software.features }}</pre>
                </div>
              </div>
            </div>

            <!-- 更新日志 -->
            <div v-if="software.changelog && software.changelog.trim()" class="section">
              <h2 class="section-title">更新日志</h2>
              <div class="section-content">
                <div class="changelog">
                  <pre>{{ software.changelog }}</pre>
                </div>
              </div>
            </div>

            <!-- 下载信息 -->
            <div class="section">
              <h2 class="section-title">下载信息</h2>
              <div class="section-content">
                <div class="download-options">
                  <!-- 本地下载信息 -->
                  <div class="download-option">
                    <h3 class="option-title">
                      <i class="el-icon-download"></i>
                      本地下载
                    </h3>
                    <div class="option-content">
                      <div v-if="software.filePath" class="file-info">
                        <p><strong>文件名:</strong> {{ software.fileName || software.name }}</p>
                        <p v-if="software.fileSize"><strong>文件大小:</strong> {{ formatFileSize(software.fileSize) }}</p>
                        <p class="file-desc">直接下载服务器上的软件文件，速度快，无需跳转</p>
                        <el-button
                          type="primary"
                          size="large"
                          @click="handleLocalDownload"
                          icon="el-icon-download"
                          class="download-btn"
                        >
                          本地下载
                        </el-button>
                      </div>
                      <div v-else class="no-file">
                        <p>暂无本地下载文件</p>
                      </div>
                    </div>
                  </div>

                  <!-- 网盘下载信息 -->
                  <div class="download-option">
                    <h3 class="option-title">
                      <i class="el-icon-link"></i>
                      网盘下载
                    </h3>
                    <div class="option-content">
                      <div v-if="software.downloadUrl" class="url-info">
                        <p class="url-desc">跳转到第三方网盘或官方网站下载</p>
                        <div class="url-actions">
                          <el-button
                            type="success"
                            size="large"
                            @click="handleNetworkDownload"
                            icon="el-icon-link"
                            class="download-btn"
                          >
                            网盘下载
                          </el-button>
                          <el-button
                            size="large"
                            @click="copyDownloadUrl"
                            icon="el-icon-document-copy"
                          >
                            复制链接
                          </el-button>
                        </div>
                      </div>
                      <div v-else class="no-url">
                        <p>暂无网盘下载地址</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !software" class="empty-state">
          <el-empty description="软件不存在或已下架"></el-empty>
        </div>
      </div>
    </div>

    <!-- 网站底部 -->
    <footer class="site-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <p class="site-desc">{{ siteConfig.siteName || '软件下载之家' }} - {{ siteConfig.siteDescription || '专业的软件下载站点' }}</p>
            <p class="site-keywords">{{ siteConfig.siteKeywords || '提供各类软件下载，包括开发工具、办公软件、图像处理、系统工具等' }}</p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 {{ siteConfig.siteName || '软件下载之家' }}. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- 图片预览对话框 -->
    <div v-if="previewVisible" class="fullscreen-preview" @click="closePreview">
      <div class="preview-controls">
        <div class="control-group">
          <button @click.stop="zoomOut" class="control-btn" title="缩小">
            <i class="el-icon-zoom-out"></i>
          </button>
          <span class="zoom-level">{{ Math.round(imageScale * 100) }}%</span>
          <button @click.stop="zoomIn" class="control-btn" title="放大">
            <i class="el-icon-zoom-in"></i>
          </button>
          <button @click.stop="rotateLeft" class="control-btn" title="左转">
            <i class="el-icon-refresh-left"></i>
          </button>
          <button @click.stop="rotateRight" class="control-btn" title="右转">
            <i class="el-icon-refresh-right"></i>
          </button>
          <button @click.stop="resetImageState" class="control-btn" title="重置">
            <i class="el-icon-refresh"></i>
          </button>
          <button @click.stop="closePreview" class="control-btn close-btn" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>
      <div class="fullscreen-image-container" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag" @mouseleave="endDrag" @wheel="onWheel">
        <img
          :src="previewImageUrl"
          class="fullscreen-preview-image"
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
        />
      </div>
    </div>
  </div>
</template>

<script>
import { softwareApi } from '@/api'
import { formatDate, parseTags, parseScreenshots } from '@/utils'

export default {
  name: 'SoftwareDetail',
  data() {
    return {
      software: null,
      loading: false,
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      searchKeyword: '',
      categoryList: [],
      showDropdown: null,
      activeCategory: null,
      siteConfig: {}
    }
  },
  computed: {
    screenshots() {
      return parseScreenshots(this.software?.screenshots)
    },

    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    }
  },
  created() {
    this.loadSoftwareDetail()
    this.loadCategoryList()
    this.loadSiteConfig()
  },
  methods: {
    formatDate,
    parseTags,

    async loadSoftwareDetail() {
      const id = this.$route.params.id
      if (!id) return

      this.loading = true
      try {
        const res = await softwareApi.getSoftwareDetail(id)
        this.software = {
          ...res.data,
          icon: res.data.icon && !res.data.icon.startsWith('http')
            ? `http://localhost:8088${res.data.icon}`
            : res.data.icon,
          screenshots: res.data.screenshots ?
            res.data.screenshots.split(',').map(url =>
              url.trim() && !url.trim().startsWith('http')
                ? `http://localhost:8088${url.trim()}`
                : url.trim()
            ).filter(Boolean).join(',')
            : ''
        }
      } catch (error) {
        console.error('加载软件详情失败:', error)
      } finally {
        this.loading = false
      }
    },

    async handleLocalDownload() {
      if (!this.software.filePath) {
        this.$message.warning('该软件暂无本地下载文件')
        return
      }

      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++

        let downloadUrl = this.software.filePath
        if (!downloadUrl.startsWith('http')) {
          downloadUrl = `http://localhost:8088${this.software.filePath}`
        }

        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = this.software.fileName || this.software.name
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('开始本地下载')
      } catch (error) {
        console.error('本地下载失败:', error)
        this.$message.error('本地下载失败')
      }
    },

    async handleNetworkDownload() {
      if (!this.software.downloadUrl) {
        this.$message.warning('该软件暂无网盘下载地址')
        return
      }

      try {
        await softwareApi.recordDownload(this.software.id)
        this.software.downloadCount++
        window.open(this.software.downloadUrl, '_blank')
        this.$message.success('正在跳转到网盘下载')
      } catch (error) {
        console.error('网盘下载失败:', error)
        this.$message.error('网盘下载失败')
      }
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
      this.resetImageState()
    },

    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    rotateLeft() {
      this.imageRotation -= 90
    },

    rotateRight() {
      this.imageRotation += 90
    },

    closePreview() {
      this.previewVisible = false
      this.previewImageUrl = ''
      this.resetImageState()
    },

    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    onDrag(event) {
      if (!this.isDragging) return
      this.imagePosition = {
        x: event.clientX - this.dragStart.x,
        y: event.clientY - this.dragStart.y
      }
    },

    endDrag() {
      this.isDragging = false
    },

    onWheel(event) {
      event.preventDefault()
      const delta = event.deltaY
      if (delta < 0) {
        this.zoomIn()
      } else {
        this.zoomOut()
      }
    },

    copyDownloadUrl() {
      navigator.clipboard.writeText(this.software.downloadUrl).then(() => {
        this.$message.success('下载地址已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$router.push({
          path: '/',
          query: { search: this.searchKeyword.trim() }
        })
      }
    },

    async loadCategoryList() {
      try {
        const res = await softwareApi.getCategoryList()
        this.categoryList = res.data || []
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },

    goToHome() {
      this.$router.push('/')
    },

    goToCategoryPage(categoryId) {
      this.$router.push({
        path: '/',
        query: { category: categoryId }
      })
    },

    isParentCategoryActive(parentId) {
      if (this.activeCategory === parentId) return true

      const parentCategory = this.categoryList.find(c => c.id === parentId)
      if (parentCategory && parentCategory.children) {
        return parentCategory.children.some(child => child.id === this.activeCategory)
      }

      return false
    },

    async loadSiteConfig() {
      try {
        const response = await softwareApi.getSiteConfig()
        this.siteConfig = response.data || {}
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    }
  }
}
</script>

<style scoped>
@import '@/styles/SoftwareDetail.css';
</style>
