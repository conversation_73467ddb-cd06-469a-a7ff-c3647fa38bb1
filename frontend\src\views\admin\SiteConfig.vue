<template>
  <div class="site-config">
    <el-card class="config-card">
      <div slot="header" class="card-header">
        <span>网站配置管理</span>
      </div>
      
      <el-form :model="configForm" :rules="configRules" ref="configForm" label-width="120px">
        <el-form-item label="网站名称" prop="siteName">
          <el-input 
            v-model="configForm.siteName" 
            placeholder="请输入网站名称"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item label="网站LOGO" prop="siteLogo">
          <el-upload
            class="logo-uploader"
            :action="uploadUrl + '/admin/upload/image'"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload">
            <img v-if="configForm.siteLogo" :src="configForm.siteLogo" class="logo-preview">
            <i v-else class="el-icon-plus logo-uploader-icon"></i>
          </el-upload>
          <div class="upload-tip">建议尺寸：100x100像素，支持jpg/png格式，不超过2MB</div>
        </el-form-item>
        
        <el-form-item label="网站描述" prop="siteDescription">
          <el-input 
            v-model="configForm.siteDescription" 
            type="textarea"
            :rows="3"
            placeholder="请输入网站描述"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item label="网站关键词" prop="siteKeywords">
          <el-input 
            v-model="configForm.siteKeywords" 
            placeholder="请输入网站关键词，多个关键词用逗号分隔"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input 
            v-model="configForm.contactEmail" 
            placeholder="请输入联系邮箱"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item label="备案号" prop="icp">
          <el-input 
            v-model="configForm.icp" 
            placeholder="请输入备案号"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="saveConfig"
            :loading="saveLoading"
          >
            {{ saveLoading ? '保存中...' : '保存配置' }}
          </el-button>
          <el-button @click="resetConfig">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { adminApi } from '@/api'

export default {
  name: 'SiteConfig',
  data() {
    return {
      uploadUrl: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8088/api',
      saveLoading: false,
      configForm: {
        siteName: '软件下载之家',
        siteLogo: '',
        siteDescription: '',
        siteKeywords: '',
        contactEmail: '',
        icp: ''
      },
      configRules: {
        siteName: [
          { required: true, message: '请输入网站名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载网站配置
    async loadConfig() {
      try {
        const response = await adminApi.getSiteConfig()
        if (response.code === 200 && response.data) {
          this.configForm = { ...this.configForm, ...response.data }
        }
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    },
    
    // 保存配置
    async saveConfig() {
      this.$refs.configForm.validate(async (valid) => {
        if (!valid) return
        
        this.saveLoading = true
        try {
          const response = await adminApi.saveSiteConfig(this.configForm)
          if (response.code === 200) {
            this.$message.success('配置保存成功')
          } else {
            this.$message.error(response.message || '配置保存失败')
          }
        } catch (error) {
          this.$message.error('配置保存失败')
          console.error('保存配置失败:', error)
        } finally {
          this.saveLoading = false
        }
      })
    },
    
    // 重置配置
    resetConfig() {
      this.$refs.configForm.resetFields()
      this.loadConfig()
    },
    
    // LOGO上传成功处理
    handleLogoSuccess(response) {
      if (response.code === 200) {
        let logoUrl = response.data.url
        if (logoUrl && !logoUrl.startsWith('http')) {
          logoUrl = 'http://localhost:8088' + logoUrl
        }
        this.configForm.siteLogo = logoUrl
        this.$message.success('LOGO上传成功')
      } else {
        this.$message.error(response.message || 'LOGO上传失败')
      }
    },
    
    // LOGO上传前验证
    beforeLogoUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    }
  }
}
</script>

<style scoped>
.site-config {
  background: white;
  padding: 0;
  border-radius: 0;
}

.config-card {
  max-width: 800px;
  border-radius: 0;
  border: none;
  box-shadow: none;
}

.config-card .el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
}

.config-card .el-card__body {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

/* 表单文字大小优化 */
.site-config .el-form-item__label {
  font-size: 14px !important;
  font-weight: 500;
}

.site-config .el-input__inner,
.site-config .el-textarea__inner {
  font-size: 14px;
}

.site-config .el-button {
  font-size: 14px;
}

/* LOGO上传样式 */
.logo-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.logo-uploader .el-upload:hover {
  border-color: #409EFF;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.logo-preview {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: contain;
  border-radius: 6px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>