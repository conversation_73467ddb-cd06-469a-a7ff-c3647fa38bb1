/*
 操作记录表创建脚本
 用于记录管理员的操作日志
 
 Date: 2025-08-01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
  `admin_username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员用户名',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：ADD-新增，UPDATE-修改，DELETE-删除，LOGIN-登录，LOGOUT-登出',
  `operation_module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作模块：SOFTWARE-软件管理，CATEGORY-分类管理，SITE_CONFIG-网站配置，PROFILE-个人信息',
  `operation_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作描述',
  `target_id` bigint(20) NULL DEFAULT NULL COMMENT '操作目标ID（如软件ID、分类ID等）',
  `target_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作目标名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求方法：GET、POST、PUT、DELETE',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求参数（JSON格式）',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `operation_status` tinyint(4) NULL DEFAULT 1 COMMENT '操作状态：1-成功，0-失败',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息（操作失败时记录）',
  `execution_time` int(11) NULL DEFAULT NULL COMMENT '执行时间（毫秒）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operation_log_admin_id`(`admin_id`) USING BTREE,
  INDEX `idx_operation_log_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_operation_log_operation_module`(`operation_module`) USING BTREE,
  INDEX `idx_operation_log_create_time`(`create_time`) USING BTREE,
  INDEX `idx_operation_log_admin_time`(`admin_id`, `create_time`) USING BTREE,
  CONSTRAINT `fk_operation_log_admin` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- 插入一些示例数据（可选）
-- ----------------------------
INSERT INTO `operation_log` (`admin_id`, `admin_username`, `operation_type`, `operation_module`, `operation_desc`, `target_id`, `target_name`, `request_method`, `request_url`, `ip_address`, `operation_status`, `create_time`) VALUES
(1, 'admin', 'LOGIN', 'PROFILE', '管理员登录系统', NULL, NULL, 'POST', '/admin/login', '127.0.0.1', 1, '2025-08-01 10:00:00'),
(1, 'admin', 'ADD', 'SOFTWARE', '添加软件：Visual Studio Code', 1, 'Visual Studio Code', 'POST', '/admin/software/add', '127.0.0.1', 1, '2025-08-01 10:30:00'),
(1, 'admin', 'UPDATE', 'SOFTWARE', '修改软件：Visual Studio Code', 1, 'Visual Studio Code', 'PUT', '/admin/software/update', '127.0.0.1', 1, '2025-08-01 11:00:00'),
(1, 'admin', 'ADD', 'CATEGORY', '添加分类：开发工具', 1, '开发工具', 'POST', '/admin/category/add', '127.0.0.1', 1, '2025-08-01 11:30:00'),
(1, 'admin', 'UPDATE', 'SITE_CONFIG', '修改网站配置', 1, '网站配置', 'PUT', '/admin/site-config/update', '127.0.0.1', 1, '2025-08-01 12:00:00');

SET FOREIGN_KEY_CHECKS = 1;
