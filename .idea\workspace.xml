<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7a219ce7-08ae-4194-a214-679286d7203d" name="更改" comment="add:首页基本上完成1">
      <change afterPath="$PROJECT_DIR$/frontend/src/styles/AdminLayout.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/frontend/src/styles/Login.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/frontend/src/styles/SoftwareDetail.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/db/software_download.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/software/controller/AdminController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/software/controller/AdminController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/software/controller/AdminController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/software/controller/AdminController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/api/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/api/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/views/SoftwareDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/views/SoftwareDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/views/SoftwareList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/views/SoftwareList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/views/admin/AdminLayout.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/views/admin/AdminLayout.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/views/admin/AdminProfile.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/views/admin/AdminProfile.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/views/admin/SoftwareManage.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/views/admin/SoftwareManage.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9" />
        <option name="localRepository" value="D:\dev\apache-maven-3.3.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="30dr98H926yS8PzjJd3goC7S7Mm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.SoftwareApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Users/<USER>/test/软件下载网站",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.serve.executor": "Run",
    "settings.editor.selected.configurable": "MavenSettings",
    "ts.external.directory.path": "D:\\Software\\dev\\JetBrains\\IntelliJ IDEA 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.SoftwareApplication">
    <configuration name="SoftwareApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="software-download" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.software.SoftwareApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="serve" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/frontend/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.serve" />
      <item itemvalue="Spring Boot.SoftwareApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7a219ce7-08ae-4194-a214-679286d7203d" name="更改" comment="" />
      <created>1753968137359</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753968137359</updated>
      <workItem from="1753968140065" duration="56000" />
      <workItem from="1754014443941" duration="8112000" />
    </task>
    <task id="LOCAL-00001" summary="add:首页基本上完成">
      <option name="closed" value="true" />
      <created>1754016217832</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754016217832</updated>
    </task>
    <task id="LOCAL-00002" summary="add:首页基本上完成1">
      <option name="closed" value="true" />
      <created>1754018079810</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754018079810</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add:首页基本上完成" />
    <MESSAGE value="add:首页基本上完成1" />
    <option name="LAST_COMMIT_MESSAGE" value="add:首页基本上完成1" />
  </component>
</project>