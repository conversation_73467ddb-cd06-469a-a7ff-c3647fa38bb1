.software-list {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 网站头部 */
.site-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 15px 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.logo img {
  width: 40px;
  height: 40px;
}

.search-area {
  flex: 1;
  max-width: 500px;
  margin: 0 50px;
}

.search-input {
  width: 100%;
}

/* 用户操作区域 */
.user-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-actions .el-button {
  font-weight: 500;
}

/* 主导航 */
.main-nav {
  background: #4a90e2;
  border-bottom: 1px solid #357abd;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav-item:hover a,
.nav-item.active a {
  background: rgba(255, 255, 255, 0.1);
}

.admin-link {
  color: #fff !important;
  padding: 15px 20px !important;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-toggle i {
  font-size: 12px;
  transition: transform 0.3s;
}

.dropdown:hover .dropdown-toggle i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 0 0 5px 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  list-style: none;
  margin: 0;
  padding: 0;
  min-width: 150px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child {
  border-bottom: none;
}

.dropdown-menu a {
  display: block;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  background: #f8f9fa;
  color: #4a90e2;
}

/* 顶部导航容器保持居中 */
.site-header .container,
.main-nav .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 主内容容器占满宽度 */
.main-content .container {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 主体内容 */
.main-content {
  padding: 20px 0;
}

.content-wrapper {
  display: flex;
  gap: 12px;
  margin: 0;
  padding: 0;
}

/* 左侧分类导航 */
.sidebar-left {
  width: 160px;
  flex-shrink: 0;
  margin-left: 15px;
}

.category-nav {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.nav-header {
  background: #4a90e2;
  color: #fff;
  padding: 12px 15px;
  border-radius: 5px 5px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.toggle-all-btn {
  font-size: 12px;
  color: #fff;
  padding: 0;
  opacity: 0.8;
}

.toggle-all-btn:hover {
  opacity: 1;
  color: #fff;
}

.category-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.category-item {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.category-item:last-child {
  border-bottom: none;
}

/* 一级分类样式 */
.parent-category .category-header {
  padding: 12px 15px;
  cursor: pointer;
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.parent-category .category-header:hover {
  background: #e9ecef;
}

.parent-category.active .category-header {
  background: #4a90e2;
  color: white;
}

.expand-icon {
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 二级分类样式 */
.sub-category-list {
  list-style: none;
  margin: 0;
  padding: 0;
  background: #fff;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.sub-category-list.show {
  max-height: 500px;
}

.sub-category-item {
  padding: 8px 30px;
  cursor: pointer;
  color: #666;
  font-size: 13px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s;
}

.sub-category-item:hover {
  background: #f8f9fa;
  color: #4a90e2;
  padding-left: 35px;
}

.sub-category-item.active {
  background: #e3f2fd;
  color: #4a90e2;
  font-weight: 500;
  border-left: 3px solid #4a90e2;
}

.sub-category-item:last-child {
  border-bottom: none;
}

/* 中间内容区 */
.content-main {
  flex: 1;
}

.breadcrumb {
  background: #fff;
  padding: 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 12px;
  color: #666;
}

.breadcrumb a {
  color: #4a90e2;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.software-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 15px;
}

.software-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.software-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 240px;
  width: calc(100% - 8px);
  position: relative;
}

.software-card:hover {
  border-color: #4a90e2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
  transform: translateY(-2px);
}

.software-icon {
  flex-shrink: 0;
  width: 90px;
  height: 90px;
  padding: 0;
  margin: 0;
  overflow: hidden;
  border-radius: 9px;
}

.software-icon img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.software-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-align: left;
  height: 100%;
  justify-content: space-between;
  padding-right: 10px;
  padding-bottom: 35px;
}

.software-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  margin: 0 0 4px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  max-width: 100%;
}

.software-version {
  font-size: 12px;
  color: #4a90e2;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.software-desc {
  color: #666;
  font-size: 12px;
  line-height: 1.3;
  margin: 0 0 1px 0;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

/* 软件标签样式 */
.software-tags {
  margin: 1px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.software-tags .tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 12px;
  color: #666;
  font-size: 11px;
  height: 18px;
  line-height: 16px;
  padding: 0 6px;
  font-weight: 400;
  white-space: nowrap;
}

.software-tags .tag-item:hover {
  background-color: #4a90e2;
  border-color: #4a90e2;
  color: #fff;
  transform: translateY(-1px);
}

.software-meta {
  font-size: 11px;
  color: #999;
  margin: 8px 0 0 0;
}

.publish-time {
  color: #999;
}

.software-actions {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
}

.software-actions .el-button {
  font-size: 11px;
  padding: 5px 10px;
  height: 28px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding: 20px 0;
}

.pagination-wrapper .el-pagination {
  text-align: center;
}

/* 右侧边栏 */
.sidebar-right {
  width: 220px;
  flex-shrink: 0;
  margin-right: 15px;
}

.widget {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  margin-bottom: 20px;
}

.widget-title {
  background: #4a90e2;
  color: #fff;
  margin: 0;
  padding: 12px 15px;
  font-size: 14px;
  border-radius: 5px 5px 0 0;
}

.widget-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.widget-list li {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.widget-list li:last-child {
  border-bottom: none;
}

.widget-list a {
  color: #333;
  text-decoration: none;
  font-size: 13px;
}

.widget-list a:hover {
  color: #4a90e2;
}

.count {
  color: #999;
  font-size: 12px;
}

/* 排行榜 */
.ranking-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.ranking-item {
  padding: 8px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.ranking-item:hover {
  background: #f8f9fa;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  background: #ccc;
}

.rank-1 { background: #ff6b6b; }
.rank-2 { background: #4ecdc4; }
.rank-3 { background: #45b7d1; }

.ranking-item .name {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.ranking-item .version {
  font-size: 11px;
  color: #999;
}

/* 最新软件 */
.latest-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.latest-item {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.latest-item:hover {
  background: #f8f9fa;
}

.latest-item:last-child {
  border-bottom: none;
}

.latest-icon img {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.latest-info {
  flex: 1;
}

.latest-info .name {
  font-size: 13px;
  color: #333;
  margin-bottom: 2px;
}

.latest-info .date {
  font-size: 11px;
  color: #999;
}

/* 分类导航动画效果 */
.parent-category {
  transition: all 0.3s ease;
}

.parent-category:hover {
  background: #f8f9fa;
}

.category-header {
  transition: all 0.2s ease;
}

.sub-category-list {
  transition: max-height 0.4s ease, opacity 0.3s ease;
  opacity: 0;
}

.sub-category-list.show {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

/* 网站底部样式 */
.site-footer {
  background: #2c3e50;
  color: #ecf0f1;
  margin-top: 40px;
  padding: 30px 0 20px;
}

.footer-content {
  margin-bottom: 20px;
}

.footer-info {
  text-align: center;
}

.site-desc {
  font-size: 16px;
  margin-bottom: 10px;
  color: #bdc3c7;
}

.site-keywords {
  font-size: 14px;
  margin-bottom: 15px;
  color: #95a5a6;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.contact-email,
.icp-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-email i,
.icp-info i {
  color: #3498db;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 15px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  font-size: 13px;
  color: #95a5a6;
}

/* 全屏图片预览样式 */
.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.preview-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 20px;
}

.fullscreen-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transform-origin: center center;
  cursor: grab;
}

.fullscreen-preview-image:active {
  cursor: grabbing;
}

/* 底部控制栏 */
.preview-controls-bottom {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
}

.controls-wrapper {
  display: flex;
  gap: 15px;
  padding: 15px 25px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.controls-wrapper .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 45px;
  height: 45px;
  font-size: 18px;
}

.controls-wrapper .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: scale(1.1);
}

.controls-wrapper .el-button:focus {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 15px;
  }

  .search-area {
    margin: 0;
    max-width: 100%;
  }

  .user-actions {
    justify-content: center;
    width: 100%;
  }

  .nav-list {
    flex-wrap: wrap;
  }

  .nav-item a {
    padding: 12px 15px;
    font-size: 14px;
  }

  .content-wrapper {
    flex-direction: column;
  }

  .sidebar-left,
  .sidebar-right {
    width: 100%;
  }

  .software-grid {
    grid-template-columns: 1fr;
  }

  .logo img {
    width: 32px;
    height: 32px;
  }

  .nav-item a {
    padding: 10px 12px;
    font-size: 13px;
  }

  .software-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
  }

  .software-card {
    padding: 15px;
    height: 120px;
    min-width: auto;
  }

  .software-icon img {
    width: 48px;
    height: 48px;
  }

  .software-name {
    font-size: 14px;
  }

  .software-desc {
    font-size: 12px;
  }

  .software-actions {
    position: static;
    margin-top: 10px;
  }

  .software-actions .el-button {
    font-size: 11px;
    height: 28px;
    padding: 4px 8px;
  }

  .preview-image-container {
    padding: 10px;
  }

  .preview-controls-bottom {
    bottom: 20px;
  }

  .controls-wrapper {
    gap: 10px;
    padding: 10px 20px;
  }

  .controls-wrapper .el-button {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

/* 大屏幕 */
@media (max-width: 1400px) {
  .software-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* 中等屏幕 */
@media (max-width: 1200px) {
  .software-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 小屏幕 */
@media (max-width: 900px) {
  .software-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 更小屏幕 */
@media (max-width: 700px) {
  .software-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .preview-controls-bottom {
    bottom: 15px;
  }

  .controls-wrapper {
    gap: 8px;
    padding: 8px 15px;
  }

  .controls-wrapper .el-button {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .nav-header {
    padding: 10px 12px;
  }

  .nav-title {
    font-size: 13px;
  }

  .toggle-all-btn {
    font-size: 11px;
  }
}
