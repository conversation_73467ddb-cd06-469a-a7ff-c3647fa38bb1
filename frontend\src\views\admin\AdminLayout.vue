<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo">
          <h3 v-show="!isCollapse">管理后台</h3>
          <i v-show="isCollapse" class="el-icon-s-grid" style="color: #ffffff; font-size: 24px;"></i>
        </div>

        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#ffffff"
          :collapse="isCollapse"
        >
          <el-menu-item index="/admin/software">
            <i class="el-icon-s-grid"></i>
            <span slot="title">软件管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/category">
            <i class="el-icon-menu"></i>
            <span slot="title">分类管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/site-config">
            <i class="el-icon-setting"></i>
            <span slot="title">网站配置</span>
          </el-menu-item>
          <el-menu-item index="/admin/profile">
            <i class="el-icon-user"></i>
            <span slot="title">个人信息</span>
          </el-menu-item>
          <el-menu-item index="/admin/operation-log">
            <i class="el-icon-document"></i>
            <span slot="title">操作记录</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              type="text"
              @click="toggleCollapse"
              class="collapse-btn"
            >
              <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
            </el-button>
            <span class="page-title">{{ pageTitle }}</span>
          </div>

          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="admin-info">
                <i class="el-icon-user-solid"></i>
                {{ adminInfo.nickname || adminInfo.username }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 修改密码对话框 -->
    <el-dialog 
      title="修改密码" 
      :visible.sync="changePasswordVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="passwordForm" 
        :rules="passwordRules" 
        ref="passwordForm" 
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelChangePassword">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmChangePassword"
          :loading="passwordLoading"
        >
          {{ passwordLoading ? '修改中...' : '确定' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { storage } from '@/utils'
import { adminApi } from '@/api'

export default {
  name: 'AdminLayout',
  data() {
    return {
      adminInfo: {},
      isCollapse: false,
      changePasswordVisible: false,
      passwordLoading: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    pageTitle() {
      const routeMap = {
        '/admin/software': '软件管理',
        '/admin/category': '分类管理',
        '/admin/site-config': '网站配置',
        '/admin/profile': '个人信息',
        '/admin/operation-log': '操作记录'
      }
      return routeMap[this.$route.path] || '管理后台'
    }
  },
  created() {
    this.checkAuth()
  },
  methods: {
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    },
    checkAuth() {
      const admin = storage.get('admin')
      if (!admin) {
        this.$message.error('请先登录')
        this.$router.push('/admin/login')
        return
      }
      this.adminInfo = admin
    },
    
    handleCommand(command) {
      if (command === 'logout') {
        this.handleLogout()
      } else if (command === 'profile') {
        this.$router.push('/admin/profile')
      } else if (command === 'changePassword') {
        this.showChangePasswordDialog()
      }
    },

    
    handleLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        storage.remove('admin')
        this.$message.success('已退出登录')
        this.$router.push('/admin/login')
      })
    },

    // 显示修改密码对话框
    showChangePasswordDialog() {
      this.changePasswordVisible = true
      this.resetPasswordForm()
    },

    // 取消修改密码
    cancelChangePassword() {
      this.changePasswordVisible = false
      this.resetPasswordForm()
    },

    // 确认修改密码
    async confirmChangePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (!valid) return
        
        this.passwordLoading = true
        try {
          await adminApi.changePassword({
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword
          })
          
          this.$message.success('密码修改成功，请重新登录')
          this.changePasswordVisible = false
          
          // 清除登录信息，跳转到登录页
          setTimeout(() => {
            storage.remove('admin')
            this.$router.push('/admin/login')
          }, 1500)
        } catch (error) {
          console.error('修改密码失败:', error)
          this.$message.error(error.response?.data?.message || '修改密码失败')
        } finally {
          this.passwordLoading = false
        }
      })
    },

    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },

    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$nextTick(() => {
        this.$refs.passwordForm && this.$refs.passwordForm.clearValidate()
      })
    }
  }
}
</script>

<style scoped>
@import '@/styles/AdminLayout.css';
</style>