import Vue from 'vue'
import VueRouter from 'vue-router'
import SoftwareList from '@/views/SoftwareList.vue'
import SoftwareDetail from '@/views/SoftwareDetail.vue'
import Login from '@/views/admin/Login.vue'
import AdminLayout from '@/views/admin/AdminLayout.vue'
import SoftwareManage from '@/views/admin/SoftwareManage.vue'
import CategoryManage from '@/views/admin/CategoryManage.vue'
import SiteConfig from '@/views/admin/SiteConfig.vue'
import AdminProfile from '@/views/admin/AdminProfile.vue'
import OperationLog from '@/views/admin/OperationLog.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: SoftwareList
  },
  {
    path: '/software/:id',
    name: 'SoftwareDetail',
    component: SoftwareDetail
  },
  {
    path: '/admin/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/admin',
    component: AdminLayout,
    children: [
      {
        path: '',
        redirect: '/admin/software'
      },
      {
        path: 'software',
        name: 'SoftwareManage',
        component: SoftwareManage,
        meta: { title: '软件管理' }
      },
      {
        path: 'category',
        name: 'CategoryManage',
        component: CategoryManage,
        meta: { title: '分类管理' }
      },
      {
        path: 'site-config',
        name: 'SiteConfig',
        component: SiteConfig,
        meta: { title: '网站配置' }
      },
        {
          path: 'profile',
          name: 'AdminProfile',
          component: () => import('@/views/admin/AdminProfile.vue'),
          meta: { title: '个人资料' }
        },
        {
          path: 'operation-log',
          name: 'OperationLog',
          component: OperationLog,
          meta: { title: '操作记录' }
        },
        {
          path: 'rich-text-test',
          name: 'RichTextTest',
          component: () => import('@/views/admin/RichTextTest.vue'),
          meta: { title: '富文本编辑器测试' }
        }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router