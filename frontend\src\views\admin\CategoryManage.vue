<template>
  <div class="category-manage">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog" icon="el-icon-plus">
        添加分类
      </el-button>
      <el-button @click="loadCategoryList" icon="el-icon-refresh">
        刷新
      </el-button>
    </div>

    <!-- 分类列表 -->
    <div class="category-container">
      <div class="category-header">
        <div class="header-item">#</div>
        <div class="header-item">分类名称</div>
        <div class="header-item">是否有效</div>
        <div class="header-item">排序</div>
        <div class="header-item">操作</div>
      </div>
      
      <div class="category-content" v-loading="loading">
        <div v-for="(category, index) in topLevelCategories" :key="category.id" class="category-group">
          <!-- 一级分类 -->
          <div class="category-row level-1">
            <div class="row-item">{{ index + 1 }}</div>
            <div class="row-item category-name">
              <div class="category-name-content">
                <button 
                  v-if="hasSubCategories(category.id)"
                  class="expand-btn"
                  @click="toggleCategory(category.id)"
                >
                  <i :class="isCategoryExpanded(category.id) ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
                </button>
                <div class="category-box">
                  <i class="el-icon-folder"></i>
                  {{ category.name }}
                </div>
                <span class="level-badge level-1-badge">一级</span>
              </div>
            </div>
            <div class="row-item">
              <el-switch
                v-model="category.status"
                :active-value="1"
                :inactive-value="0"
                @change="updateCategoryStatus(category)"
                size="small"
              />
            </div>
            <div class="row-item">{{ category.sortOrder }}</div>
            <div class="row-item">
              <el-button size="mini" type="primary" @click="editCategory(category)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteCategory(category.id)">删除</el-button>
            </div>
          </div>
          
          <!-- 二级分类 (只在展开时显示) -->
          <div v-if="isCategoryExpanded(category.id)" v-for="subCategory in getSubCategories(category.id)" :key="subCategory.id" class="category-row level-2">
            <div class="row-item"></div>
            <div class="row-item category-name">
              <div class="category-name-content sub-content">
                <div class="category-box sub-category">
                  <i class="el-icon-document"></i>
                  {{ subCategory.name }}
                </div>
                <span class="level-badge level-2-badge">二级</span>
              </div>
            </div>
            <div class="row-item">
              <el-switch
                v-model="subCategory.status"
                :active-value="1"
                :inactive-value="0"
                @change="updateCategoryStatus(subCategory)"
                size="small"
              />
            </div>
            <div class="row-item">{{ subCategory.sortOrder }}</div>
            <div class="row-item">
              <el-button size="mini" type="primary" @click="editCategory(subCategory)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteCategory(subCategory.id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="categoryForm" :rules="rules" ref="categoryForm" label-width="120px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        
        <el-form-item label="分类级别" prop="categoryLevel">
          <el-radio-group v-model="categoryLevel" @change="handleCategoryLevelChange">
            <el-radio :label="1">一级分类</el-radio>
            <el-radio :label="2">二级分类</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="categoryLevel === 2" label="父级分类" prop="parentId">
          <el-select v-model="categoryForm.parentId" placeholder="请选择父级分类" style="width: 100%">
            <el-option
              v-for="category in topLevelCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id">
              <span>
                <i class="el-icon-folder" style="margin-right: 5px;"></i>
                {{ category.name }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="categoryForm.description" placeholder="请输入分类描述"></el-input>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" :max="999"></el-input-number>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitCategory" :loading="submitLoading">
          {{ submitLoading ? '保存中...' : '确定' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { adminApi } from '@/api'

export default {
  name: 'CategoryManage',
  data() {
    return {
      categoryList: [],
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: '添加分类',
      isEdit: false,
      categoryLevel: 1, // 分类级别：1-一级分类，2-二级分类
      expandedCategories: {}, // 记录展开状态的对象
      categoryForm: {
        id: null,
        name: '',
        parentId: 0,
        description: '',
        sortOrder: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        parentId: [
          { validator: this.validateParentId, trigger: 'change' }
        ],
        description: [
          { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
        ],
        sortOrder: [
          { required: true, message: '请输入排序值', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    // 获取一级分类
    topLevelCategories() {
      return this.categoryList
        .filter(c => (!c.parentId || c.parentId === 0))
        .sort((a, b) => a.sortOrder - b.sortOrder)
    }
  },
  
  mounted() {
    this.loadCategoryList()
  },
  
  methods: {
    // 加载分类列表
    async loadCategoryList() {
      this.loading = true
      try {
        const response = await adminApi.getCategoryList()
        if (response.code === 200) {
          this.categoryList = response.data || []
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        this.$message.error('加载分类列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取子分类
    getSubCategories(parentId) {
      return this.categoryList
        .filter(c => c.parentId === parentId)
        .sort((a, b) => a.sortOrder - b.sortOrder)
    },

    // 切换分类展开状态
    toggleCategory(categoryId) {
      this.$set(this.expandedCategories, categoryId, !this.expandedCategories[categoryId])
    },

    // 检查分类是否展开
    isCategoryExpanded(categoryId) {
      return !!this.expandedCategories[categoryId]
    },

    // 检查是否有子分类
    hasSubCategories(categoryId) {
      return this.categoryList.some(c => c.parentId === categoryId)
    },

    // 处理分类级别变化
    handleCategoryLevelChange(level) {
      if (level === 1) {
        this.categoryForm.parentId = null
      } else if (level === 2) {
        if (this.categoryForm.parentId === null || this.categoryForm.parentId === 0) {
          this.categoryForm.parentId = null
        }
      }
    },

    // 验证父级分类
    validateParentId(rule, value, callback) {
      if (this.categoryLevel === 2) {
        if (!value || value === 0) {
          callback(new Error('二级分类必须选择父级分类'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    // 显示添加对话框
    showAddDialog() {
      this.dialogTitle = '添加分类'
      this.isEdit = false
      this.categoryLevel = 1
      this.categoryForm = {
        id: null,
        name: '',
        parentId: null,
        description: '',
        sortOrder: 0,
        status: 1
      }
      this.dialogVisible = true
    },

    // 编辑分类
    editCategory(category) {
      this.dialogTitle = '编辑分类'
      this.isEdit = true
      this.categoryForm = { ...category }
      
      // 根据是否有父级分类设置分类级别
      if (!category.parentId || category.parentId === 0) {
        this.categoryLevel = 1
        this.categoryForm.parentId = null
      } else {
        this.categoryLevel = 2
      }
      
      this.dialogVisible = true
    },

    // 提交分类
    async submitCategory() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const formData = { ...this.categoryForm }
            if (this.categoryLevel === 1) {
              formData.parentId = null
            }

            let response
            if (this.isEdit) {
              response = await adminApi.updateCategory(formData)
            } else {
              response = await adminApi.addCategory(formData)
            }

            if (response.code === 200) {
              this.$message.success(response.message)
              this.dialogVisible = false
              this.loadCategoryList()
            } else {
              this.$message.error(response.message)
            }
          } catch (error) {
            this.$message.error('操作失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 更新分类状态
    async updateCategoryStatus(category) {
      try {
        const response = await adminApi.updateCategory(category)
        if (response.code === 200) {
          this.$message.success('状态更新成功')
        } else {
          this.$message.error(response.message)
          // 恢复原状态
          category.status = category.status === 1 ? 0 : 1
        }
      } catch (error) {
        this.$message.error('状态更新失败')
        // 恢复原状态
        category.status = category.status === 1 ? 0 : 1
      }
    },

    // 删除分类
    async deleteCategory(id) {
      // 检查是否有子分类
      const hasSubCategories = this.categoryList.some(c => c.parentId === id)
      if (hasSubCategories) {
        this.$message.warning('该分类下还有子分类，请先删除子分类')
        return
      }

      this.$confirm('确定要删除这个分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await adminApi.deleteCategory(id)
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.loadCategoryList()
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.category-manage {
  background: white;
  padding: 0;
  border-radius: 0;
}

.toolbar {
  margin-bottom: 0;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
}

.category-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.category-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.header-item {
  padding: 10px 12px;
  border-right: 1px solid #ebeef5;
}

.header-item:first-child {
  width: 60px;
  text-align: center;
}

.header-item:nth-child(2) {
  flex: 1;
}

.header-item:nth-child(3) {
  width: 100px;
  text-align: center;
}

.header-item:nth-child(4) {
  width: 80px;
  text-align: center;
}

.header-item:nth-child(5) {
  width: 160px;
  text-align: center;
}

.header-item:last-child {
  border-right: none;
}

.category-content {
  min-height: 200px;
}

.category-group {
  border-bottom: 1px solid #f0f0f0;
}

.category-group:last-child {
  border-bottom: none;
}

.category-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f8f8f8;
  transition: background-color 0.2s;
  font-size: 12px;
}

.category-row:hover {
  background-color: #f5f7fa;
}

.category-row.level-1 {
  background-color: #fafafa;
  border-left: 4px solid #409eff;
}

.category-row.level-2 {
  background-color: #fff;
  border-left: 4px solid #67c23a;
  margin-left: 20px;
}

.row-item {
  padding: 8px 12px;
  border-right: 1px solid #ebeef5;
  font-size: 12px;
}

.row-item:first-child {
  width: 60px;
  text-align: center;
  font-weight: 500;
}

.row-item:nth-child(2) {
  flex: 1;
}

.row-item:nth-child(3) {
  width: 100px;
  text-align: center;
}

.row-item:nth-child(4) {
  width: 80px;
  text-align: center;
}

.row-item:nth-child(5) {
  width: 160px;
  text-align: center;
}

.row-item:last-child {
  border-right: none;
}

.category-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-name-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.category-name-content.sub-content {
  margin-left: 20px;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 6px;
  margin-right: 8px;
  color: #606266;
  font-size: 14px;
  transition: color 0.2s;
}

.expand-btn:hover {
  color: #409eff;
}

.category-box {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 3px;
  color: #1f2937;
  font-weight: 500;
  font-size: 11px;
}

.category-box.sub-category {
  background-color: #f0f9f0;
  border-color: #b3d8b3;
}

.category-box i {
  margin-right: 4px;
  color: #409eff;
  font-size: 12px;
}

.category-box.sub-category i {
  color: #67c23a;
}

.level-badge {
  padding: 1px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 8px;
}

.level-1-badge {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.level-2-badge {
  background-color: #f0f9f0;
  color: #67c23a;
  border: 1px solid #b3d8b3;
}

.dialog-footer {
  text-align: right;
}

/* 按钮样式调整 */
.el-button--mini {
  padding: 4px 8px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-row.level-2 {
    margin-left: 10px;
  }
  
  .category-box {
    padding: 3px 6px;
    font-size: 10px;
  }
  
  .level-badge {
    display: none;
  }
  
  .expand-btn {
    padding: 1px 4px;
    font-size: 12px;
  }
}
</style>
