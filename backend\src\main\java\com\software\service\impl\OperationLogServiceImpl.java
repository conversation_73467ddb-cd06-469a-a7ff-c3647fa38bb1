package com.software.service.impl;

import com.software.entity.OperationLog;
import com.software.mapper.OperationLogMapper;
import com.software.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {
    
    @Autowired
    private OperationLogMapper operationLogMapper;
    
    @Override
    public void recordOperation(OperationLog operationLog) {
        if (operationLog.getCreateTime() == null) {
            operationLog.setCreateTime(LocalDateTime.now());
        }
        if (operationLog.getOperationStatus() == null) {
            operationLog.setOperationStatus(OperationLog.OPERATION_STATUS_SUCCESS);
        }
        operationLogMapper.insert(operationLog);
    }
    
    @Override
    public void recordOperation(Long adminId, String adminUsername, String operationType, 
                               String operationModule, String operationDesc, 
                               Long targetId, String targetName) {
        OperationLog operationLog = new OperationLog();
        operationLog.setAdminId(adminId);
        operationLog.setAdminUsername(adminUsername);
        operationLog.setOperationType(operationType);
        operationLog.setOperationModule(operationModule);
        operationLog.setOperationDesc(operationDesc);
        operationLog.setTargetId(targetId);
        operationLog.setTargetName(targetName);
        operationLog.setOperationStatus(OperationLog.OPERATION_STATUS_SUCCESS);
        operationLog.setCreateTime(LocalDateTime.now());
        
        operationLogMapper.insert(operationLog);
    }
    
    @Override
    public void recordOperation(Long adminId, String adminUsername, String operationType, 
                               String operationModule, String operationDesc, 
                               Long targetId, String targetName, String requestMethod, 
                               String requestUrl, String requestParams, String ipAddress, 
                               String userAgent) {
        OperationLog operationLog = new OperationLog();
        operationLog.setAdminId(adminId);
        operationLog.setAdminUsername(adminUsername);
        operationLog.setOperationType(operationType);
        operationLog.setOperationModule(operationModule);
        operationLog.setOperationDesc(operationDesc);
        operationLog.setTargetId(targetId);
        operationLog.setTargetName(targetName);
        operationLog.setRequestMethod(requestMethod);
        operationLog.setRequestUrl(requestUrl);
        operationLog.setRequestParams(requestParams);
        operationLog.setIpAddress(ipAddress);
        operationLog.setUserAgent(userAgent);
        operationLog.setOperationStatus(OperationLog.OPERATION_STATUS_SUCCESS);
        operationLog.setCreateTime(LocalDateTime.now());
        
        operationLogMapper.insert(operationLog);
    }
    
    @Override
    public Map<String, Object> getOperationLogsByAdminId(Long adminId, int page, int size) {
        int offset = (page - 1) * size;
        List<OperationLog> logs = operationLogMapper.selectByAdminId(adminId, offset, size);
        int total = operationLogMapper.countByAdminId(adminId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", logs);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        
        return result;
    }
    
    @Override
    public List<OperationLog> getRecentOperationLogs(Long adminId, int limit) {
        return operationLogMapper.selectRecentByAdminId(adminId, limit);
    }
    
    @Override
    public Map<String, Object> getOperationLogsByType(Long adminId, String operationType, int page, int size) {
        int offset = (page - 1) * size;
        List<OperationLog> logs = operationLogMapper.selectByAdminIdAndType(adminId, operationType, offset, size);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", logs);
        result.put("page", page);
        result.put("size", size);
        
        return result;
    }
    
    @Override
    public Map<String, Object> getOperationLogsByModule(Long adminId, String operationModule, int page, int size) {
        int offset = (page - 1) * size;
        List<OperationLog> logs = operationLogMapper.selectByAdminIdAndModule(adminId, operationModule, offset, size);

        Map<String, Object> result = new HashMap<>();
        result.put("list", logs);
        result.put("page", page);
        result.put("size", size);

        return result;
    }

    @Override
    public Map<String, Object> getOperationLogsByCondition(Long adminId, String operationType, String operationModule, int page, int size) {
        int offset = (page - 1) * size;
        List<OperationLog> logs = operationLogMapper.selectByCondition(adminId, operationType, operationModule, offset, size);
        int total = operationLogMapper.countByCondition(adminId, operationType, operationModule);

        Map<String, Object> result = new HashMap<>();
        result.put("list", logs);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);

        return result;
    }

    @Override
    public int cleanOldRecords(int days) {
        return operationLogMapper.deleteOldRecords(days);
    }
    
    @Override
    public Map<String, Object> getAllOperationLogs(int page, int size) {
        int offset = (page - 1) * size;
        List<OperationLog> logs = operationLogMapper.selectAll(offset, size);
        int total = operationLogMapper.countAll();
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", logs);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        
        return result;
    }
}
