.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #ffffff;
  min-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e6e6;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
}

.logo h3 {
  color: #333333;
  margin: 0;
  font-weight: 600;
  font-size: 1.2rem;
}

.el-menu {
  border-right: none;
  min-height: calc(100vh - 60px);
  height: auto;
  flex: 1;
}

.el-menu-item {
  border-radius: 6px;
  margin: 4px 12px;
  transition: all 0.3s ease;
}

.el-menu-item:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-menu-item.is-active {
  background-color: #ecf5ff !important;
  color: #409eff !important;
  border-right: 3px solid #409eff;
}

.header {
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.page-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.admin-info {
  cursor: pointer;
  color: #666;
  font-size: 0.9rem;
}

.admin-info:hover {
  color: #409eff;
}

.main-content {
  background: #ffffff;
  padding: 24px;
  min-height: calc(100vh - 60px);
}

@media (max-width: 768px) {
  .el-aside {
    width: 200px !important;
  }
  
  .header {
    padding: 0 15px;
  }
  
  .main-content {
    padding: 15px;
  }
}
