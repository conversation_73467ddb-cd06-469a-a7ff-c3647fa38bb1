.admin-layout {
  height: 100vh;
}

.sidebar {
  width: 150px;
  background-color: #304156;
  min-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #2b3a4b;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  border-bottom: 1px solid #1f2d3d;
}

.logo h3 {
  color: #ffffff;
  margin: 0;
  font-weight: 600;
  font-size: 1.2rem;
}

.el-menu {
  border-right: none;
  min-height: calc(100vh - 60px);
  height: auto;
  flex: 1;
}

.el-menu-item {
  border-radius: 0;
  margin: 0;
  transition: all 0.3s ease;
  color: #bfcbd9 !important;
}

.el-menu-item:hover {
  background-color: #263445 !important;
  color: #409eff !important;
}

.el-menu-item.is-active {
  background-color: #409eff !important;
  color: #ffffff !important;
  border-right: none;
}

.header {
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.page-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.admin-info {
  cursor: pointer;
  color: #666;
  font-size: 0.9rem;
}

.admin-info:hover {
  color: #409eff;
}

.main-content {
  background: rgb(245, 245, 245);
  padding: 5px;
  min-height: calc(100vh - 60px);
}

@media (max-width: 768px) {
  .el-aside {
    width: 200px !important;
  }
  
  .header {
    padding: 0 15px;
  }
  
  .main-content {
    padding: 15px;
  }
}
