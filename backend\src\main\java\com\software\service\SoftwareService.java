package com.software.service;

import com.software.entity.Software;
import com.software.mapper.SoftwareMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 软件服务类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class SoftwareService {
    
    @Autowired
    private SoftwareMapper softwareMapper;
    
    /**
     * 获取所有正常状态的软件列表
     */
    public List<Software> getAllActiveSoftware() {
        return softwareMapper.selectAllActive();
    }
    
    /**
     * 根据ID获取软件详情
     */
    public Software getSoftwareById(Long id) {
        return softwareMapper.selectById(id);
    }
    
    /**
     * 记录软件下载
     */
    public boolean recordDownload(Long id) {
        return softwareMapper.updateDownloadCount(id) > 0;
    }
    
    /**
     * 获取所有软件（管理员用）
     */
    public List<Software> getAllSoftware() {
        return softwareMapper.selectAll();
    }
    
    /**
     * 添加软件
     */
    public boolean addSoftware(Software software) {
        if (software.getPublishTime() == null) {
            software.setPublishTime(LocalDateTime.now());
        }
        if (software.getStatus() == null) {
            software.setStatus(1);
        }
        if (software.getDownloadCount() == null) {
            software.setDownloadCount(0);
        }
        return softwareMapper.insert(software) > 0;
    }
    
    /**
     * 更新软件
     */
    public boolean updateSoftware(Software software) {
        return softwareMapper.update(software) > 0;
    }
    
    /**
     * 删除软件
     */
    public boolean deleteSoftware(Long id) {
        return softwareMapper.deleteById(id) > 0;
    }
    
    /**
     * 根据关键词搜索软件（支持多字段搜索）
     */
    public List<Software> searchSoftware(String keyword) {
        // 获取所有软件，然后在内存中进行多字段搜索
        List<Software> allSoftware = softwareMapper.selectAllActive();
        if (keyword == null || keyword.trim().isEmpty()) {
            return allSoftware;
        }

        String searchKeyword = keyword.toLowerCase().trim();
        return allSoftware.stream()
            .filter(software -> {
                // 搜索软件名称
                boolean nameMatch = software.getName() != null &&
                    software.getName().toLowerCase().contains(searchKeyword);

                // 搜索软件描述
                boolean descMatch = software.getDescription() != null &&
                    software.getDescription().toLowerCase().contains(searchKeyword);

                // 搜索详细描述
                boolean detailDescMatch = software.getDetailDescription() != null &&
                    software.getDetailDescription().toLowerCase().contains(searchKeyword);

                // 搜索发布商
                boolean publisherMatch = software.getPublisher() != null &&
                    software.getPublisher().toLowerCase().contains(searchKeyword);

                // 搜索标签
                boolean tagsMatch = software.getTags() != null &&
                    software.getTags().toLowerCase().contains(searchKeyword);

                return nameMatch || descMatch || detailDescMatch || publisherMatch || tagsMatch;
            })
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取热门软件（按下载量排序）
     */
    public List<Software> getTopSoftware(int limit) {
        return softwareMapper.selectTopByDownloadCount(limit);
    }

    /**
     * 获取最新软件（按发布时间排序）
     */
    public List<Software> getLatestSoftware(int limit) {
        return softwareMapper.selectLatestByPublishTime(limit);
    }
}
