package com.software.controller;

import com.software.dto.LoginRequest;
import com.software.dto.Result;
import com.software.entity.Admin;
import com.software.entity.Category;
import com.software.entity.OperationLog;
import com.software.entity.Software;
import com.software.entity.SiteConfig;
import com.software.service.AdminService;
import com.software.service.CategoryService;
import com.software.service.OperationLogService;
import com.software.service.SoftwareService;
import com.software.service.SiteConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 管理员控制器
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private SiteConfigService siteConfigService;

    @Autowired
    private OperationLogService operationLogService;

    @Value("${file.upload.base-path}")
    private String basePath;

    @Value("${file.upload.image-path}")
    private String imagePath;

    @Value("${file.upload.software-path}")
    private String softwarePath;

    @Value("${file.upload.access-url}")
    private String accessUrl;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Admin> login(@RequestBody LoginRequest loginRequest) {
        try {
            Admin admin = adminService.login(loginRequest.getUsername(), loginRequest.getPassword());
            if (admin != null) {
                return Result.success("登录成功", admin);
            } else {
                return Result.error(401, "用户名或密码错误");
            }
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<String> changePassword(@RequestBody Map<String, String> request) {
        try {
            String oldPassword = request.get("oldPassword");
            String newPassword = request.get("newPassword");
            
            if (oldPassword == null || newPassword == null) {
                return Result.error("参数不能为空");
            }
            
            boolean success = adminService.changePassword(oldPassword, newPassword);
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("原密码错误");
            }
        } catch (Exception e) {
            return Result.error("修改密码失败：" + e.getMessage());
        }
    }

    /**
     * 更新个人信息
     */
    @PostMapping("/update-profile")
    public Result<String> updateProfile(@RequestBody Admin admin) {
        try {
            boolean success = adminService.updateProfile(admin);
            if (success) {
                return Result.success("个人信息更新成功");
            } else {
                return Result.error("个人信息更新失败");
            }
        } catch (Exception e) {
            return Result.error("个人信息更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取软件列表（管理员用，支持分页和搜索）
     */
    @GetMapping("/software/list")
    public Result<Map<String, Object>> getSoftwareList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Integer status) {
        try {
            // 打印搜索参数用于调试
            System.out.println("搜索参数 - name: " + name + ", description: " + description +
                             ", categoryId: " + categoryId + ", status: " + status);

            List<Software> allSoftware = softwareService.getAllSoftware();
            System.out.println("总软件数量: " + allSoftware.size());

            // 过滤数据
            List<Software> filteredList = allSoftware.stream()
                .filter(software -> {
                    // 软件名称模糊搜索
                    boolean matchName = true;
                    if (name != null && !name.trim().isEmpty()) {
                        String searchName = name.trim().toLowerCase();
                        String softwareName = software.getName() != null ? software.getName().toLowerCase() : "";
                        matchName = softwareName.contains(searchName);
                    }

                    // 软件说明模糊搜索
                    boolean matchDescription = true;
                    if (description != null && !description.trim().isEmpty()) {
                        String searchDesc = description.trim().toLowerCase();
                        String softwareDesc = software.getDescription() != null ? software.getDescription().toLowerCase() : "";
                        matchDescription = softwareDesc.contains(searchDesc);
                    }

                    // 分类精确匹配
                    boolean matchCategory = true;
                    if (categoryId != null) {
                        matchCategory = software.getCategoryId() != null && software.getCategoryId().equals(categoryId);
                    }

                    // 状态精确匹配
                    boolean matchStatus = true;
                    if (status != null) {
                        matchStatus = software.getStatus() != null && software.getStatus().equals(status);
                    }

                    return matchName && matchDescription && matchCategory && matchStatus;
                })
                .collect(java.util.stream.Collectors.toList());

            System.out.println("过滤后软件数量: " + filteredList.size());

            // 分页处理
            int total = filteredList.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            
            List<Software> pageList = filteredList.subList(startIndex, endIndex);
            
            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", pageList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取软件列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加软件
     */
    @PostMapping("/software")
    public Result<String> addSoftware(@RequestBody Software software, HttpServletRequest request) {
        try {
            boolean success = softwareService.addSoftware(software);
            if (success) {
                // 记录操作日志
                operationLogService.recordOperation(
                    1L, // 暂时使用固定管理员ID，实际应该从token中获取
                    "admin", // 暂时使用固定用户名，实际应该从token中获取
                    OperationLog.OPERATION_TYPE_ADD,
                    OperationLog.OPERATION_MODULE_SOFTWARE,
                    "添加软件：" + software.getName(),
                    software.getId(),
                    software.getName(),
                    "POST",
                    "/admin/software",
                    null,
                    getClientIpAddress(request),
                    request.getHeader("User-Agent")
                );
                return Result.success("添加软件成功");
            } else {
                return Result.error("添加软件失败");
            }
        } catch (Exception e) {
            return Result.error("添加软件失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新软件
     */
    @PutMapping("/software/{id}")
    public Result<String> updateSoftware(@PathVariable Long id, @RequestBody Software software) {
        try {
            software.setId(id);
            boolean success = softwareService.updateSoftware(software);
            if (success) {
                return Result.success("更新软件成功");
            } else {
                return Result.error("更新软件失败");
            }
        } catch (Exception e) {
            return Result.error("更新软件失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除软件
     */
    @DeleteMapping("/software/{id}")
    public Result<String> deleteSoftware(@PathVariable Long id) {
        try {
            boolean success = softwareService.deleteSoftware(id);
            if (success) {
                return Result.success("删除软件成功");
            } else {
                return Result.error("删除软件失败");
            }
        } catch (Exception e) {
            return Result.error("删除软件失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取软件详情（管理员用）
     */
    @GetMapping("/software/{id}")
    public Result<Software> getSoftwareDetail(@PathVariable Long id) {
        try {
            Software software = softwareService.getSoftwareById(id);
            if (software == null) {
                return Result.error(404, "软件不存在");
            }
            return Result.success(software);
        } catch (Exception e) {
            return Result.error("获取软件详情失败：" + e.getMessage());
        }
    }

    // ==================== 分类管理接口 ====================

    /**
     * 获取所有分类列表（管理员用）
     */
    @GetMapping("/category/list")
    public Result<List<Category>> getAllCategories() {
        try {
            List<Category> categoryList = categoryService.getAllCategories();
            return Result.success(categoryList);
        } catch (Exception e) {
            return Result.error("获取分类列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加分类
     */
    @PostMapping("/category/add")
    public Result<String> addCategory(@RequestBody Category category) {
        try {
            // 检查分类名称是否已存在
            Category existingCategory = categoryService.getCategoryByName(category.getName());
            if (existingCategory != null) {
                return Result.error("分类名称已存在");
            }

            boolean success = categoryService.addCategory(category);
            if (success) {
                return Result.success("分类添加成功");
            } else {
                return Result.error("分类添加失败");
            }
        } catch (Exception e) {
            return Result.error("分类添加失败：" + e.getMessage());
        }
    }

    /**
     * 更新分类
     */
    @PostMapping("/category/update")
    public Result<String> updateCategory(@RequestBody Category category) {
        try {
            boolean success = categoryService.updateCategory(category);
            if (success) {
                return Result.success("分类更新成功");
            } else {
                return Result.error("分类更新失败");
            }
        } catch (Exception e) {
            return Result.error("分类更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @PostMapping("/category/delete/{id}")
    public Result<String> deleteCategory(@PathVariable Long id) {
        try {
            boolean success = categoryService.deleteCategory(id);
            if (success) {
                return Result.success("分类删除成功");
            } else {
                return Result.error("分类删除失败");
            }
        } catch (Exception e) {
            return Result.error("分类删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取分类详情
     */
    @GetMapping("/category/{id}")
    public Result<Category> getCategoryDetail(@PathVariable Long id) {
        try {
            Category category = categoryService.getCategoryById(id);
            if (category == null) {
                return Result.error(404, "分类不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            return Result.error("获取分类详情失败：" + e.getMessage());
        }
    }

    // ==================== 网站配置管理接口 ====================

    /**
     * 获取网站配置
     */
    @GetMapping("/site-config")
    public Result<SiteConfig> getSiteConfig() {
        try {
            SiteConfig siteConfig = siteConfigService.getSiteConfig();
            return Result.success(siteConfig);
        } catch (Exception e) {
            return Result.error("获取网站配置失败：" + e.getMessage());
        }
    }

    /**
     * 保存网站配置
     */
    @PostMapping("/site-config")
    public Result<String> saveSiteConfig(@RequestBody SiteConfig siteConfig) {
        try {
            boolean success = siteConfigService.saveSiteConfig(siteConfig);
            if (success) {
                return Result.success("网站配置保存成功");
            } else {
                return Result.error("网站配置保存失败");
            }
        } catch (Exception e) {
            return Result.error("网站配置保存失败：" + e.getMessage());
        }
    }

    // ==================== 文件上传接口 ====================

    /**
     * 上传图片文件（软件图标、截图等）
     */
    @PostMapping("/upload/image")
    public Result<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }

            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error("只能上传图片文件");
            }

            // 创建上传目录
            String imageDir = basePath + imagePath;
            File dir = new File(imageDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 保持原文件名，如果重复则添加时间戳
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String nameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf("."));
            String filename = originalFilename;
            
            // 检查文件是否已存在，如果存在则添加时间戳
            File targetFile = new File(imageDir + filename);
            if (targetFile.exists()) {
                filename = nameWithoutExt + "_" + System.currentTimeMillis() + extension;
            }

            // 保存文件
            Path filePath = Paths.get(imageDir + filename);
            Files.copy(file.getInputStream(), filePath);

            // 返回文件访问URL（包含context-path）
            Map<String, String> result = new HashMap<>();
            result.put("url", "/api" + accessUrl + imagePath + filename);
            result.put("filename", filename);

            return Result.success("图片上传成功", result);
        } catch (IOException e) {
            return Result.error("图片上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传软件文件（exe、压缩包等）
     */
    @PostMapping("/upload/software")
    public Result<Map<String, Object>> uploadSoftware(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }

            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
            if (!extension.matches("\\.(exe|zip|rar|7z|msi|dmg|pkg|deb|rpm)$")) {
                return Result.error("只能上传软件安装包文件（exe、zip、rar、7z、msi、dmg、pkg、deb、rpm）");
            }

            // 创建上传目录
            String softwareDir = basePath + softwarePath;
            File dir = new File(softwareDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 保持原文件名，如果重复则添加时间戳
            String nameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf("."));
            String filename = originalFilename;
            
            // 检查文件是否已存在，如果存在则添加时间戳
            File targetFile = new File(softwareDir + filename);
            if (targetFile.exists()) {
                filename = nameWithoutExt + "_" + System.currentTimeMillis() + extension;
            }

            // 保存文件
            Path filePath = Paths.get(softwareDir + filename);
            Files.copy(file.getInputStream(), filePath);

            // 返回文件信息（包含context-path）
            Map<String, Object> result = new HashMap<>();
            result.put("filePath", "/api" + accessUrl + softwarePath + filename);
            result.put("fileName", originalFilename);
            result.put("fileSize", file.getSize());

            return Result.success("软件文件上传成功", result);
        } catch (IOException e) {
            return Result.error("软件文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作记录列表（分页）
     */
    @GetMapping("/operation-logs")
    public Result<Map<String, Object>> getOperationLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest request) {
        try {
            // 暂时使用固定的管理员ID，后续可以从session或token中获取
            Long adminId = 1L;

            Map<String, Object> result = operationLogService.getOperationLogsByAdminId(adminId, page, size);
            return Result.success("获取操作记录成功", result);
        } catch (Exception e) {
            return Result.error("获取操作记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取最近操作记录
     */
    @GetMapping("/operation-logs/recent")
    public Result<List<OperationLog>> getRecentOperationLogs(
            @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        try {
            // 暂时使用固定的管理员ID，后续可以从session或token中获取
            Long adminId = 1L;

            List<OperationLog> logs = operationLogService.getRecentOperationLogs(adminId, limit);
            return Result.success("获取最近操作记录成功", logs);
        } catch (Exception e) {
            return Result.error("获取最近操作记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}