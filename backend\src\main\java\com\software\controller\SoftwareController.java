package com.software.controller;

import com.software.dto.CategoryTreeDto;
import com.software.dto.Result;
import com.software.entity.Software;
import com.software.entity.Category;
import com.software.service.SoftwareService;
import com.software.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 软件控制器
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/software")
public class SoftwareController {
    
    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private CategoryService categoryService;
    
    /**
     * 获取软件列表
     */
    @GetMapping("/list")
    public Result<List<Software>> getSoftwareList() {
        try {
            List<Software> softwareList = softwareService.getAllActiveSoftware();
            return Result.success(softwareList);
        } catch (Exception e) {
            return Result.error("获取软件列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取软件详情
     */
    @GetMapping("/{id}")
    public Result<Software> getSoftwareDetail(@PathVariable Long id) {
        try {
            Software software = softwareService.getSoftwareById(id);
            if (software == null) {
                return Result.error(404, "软件不存在");
            }
            return Result.success(software);
        } catch (Exception e) {
            return Result.error("获取软件详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 记录软件下载
     */
    @PostMapping("/download/{id}")
    public Result<String> recordDownload(@PathVariable Long id) {
        try {
            boolean success = softwareService.recordDownload(id);
            if (success) {
                return Result.success("下载记录成功");
            } else {
                return Result.error("下载记录失败");
            }
        } catch (Exception e) {
            return Result.error("下载记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 搜索软件
     */
    @GetMapping("/search")
    public Result<List<Software>> searchSoftware(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String keyword) {
        try {
            // 兼容两种参数名
            String searchKeyword = keyword != null ? keyword : name;
            if (searchKeyword == null || searchKeyword.trim().isEmpty()) {
                return Result.error("搜索关键词不能为空");
            }

            List<Software> softwareList = softwareService.searchSoftware(searchKeyword);
            return Result.success(softwareList);
        } catch (Exception e) {
            return Result.error("搜索软件失败：" + e.getMessage());
        }
    }

    /**
     * 获取分类树结构（一级分类及其子分类）
     */
    @GetMapping("/categories")
    public Result<List<CategoryTreeDto>> getCategoryTree() {
        try {
            List<CategoryTreeDto> categoryTree = categoryService.getCategoryTree();
            return Result.success(categoryTree);
        } catch (Exception e) {
            return Result.error("获取分类列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取热门软件
     */
    @GetMapping("/top")
    public Result<List<Software>> getTopSoftware(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Software> softwareList = softwareService.getTopSoftware(limit);
            return Result.success(softwareList);
        } catch (Exception e) {
            return Result.error("获取热门软件失败：" + e.getMessage());
        }
    }

    /**
     * 获取最新软件
     */
    @GetMapping("/latest")
    public Result<List<Software>> getLatestSoftware(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Software> softwareList = softwareService.getLatestSoftware(limit);
            return Result.success(softwareList);
        } catch (Exception e) {
            return Result.error("获取最新软件失败：" + e.getMessage());
        }
    }
}
