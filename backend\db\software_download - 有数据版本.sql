/*
 Navicat Premium Data Transfer

 Source Server         : MySQL5.0
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : software_download

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 31/07/2025 21:20:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像地址',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES (1, 'admin', '123456', '系统管理员', '<EMAIL>', '13588698846', 'http://localhost:8088/api/uploads/images/89b8b81f-0bf7-412d-afb9-6648663a18a4.jpg', '2025-07-30 15:00:37', 1, '2025-07-30 15:00:37', '2025-07-31 16:17:06');

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父分类ID，NULL表示一级分类',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_category_status_parent`(`status`, `parent_id`) USING BTREE,
  CONSTRAINT `fk_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '软件分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of category
-- ----------------------------
INSERT INTO `category` VALUES (1, '开发工具', '编程开发相关软件工具', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (2, '办公软件', '办公和文档处理软件', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (3, '图像处理', '图片编辑和设计软件', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (4, '系统工具', '系统优化和管理工具', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (5, '网络工具', '网络和通讯软件', NULL, 5, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (6, '娱乐软件', '游戏和娱乐应用', NULL, 6, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', NULL);
INSERT INTO `category` VALUES (7, '代码编辑器', 'IDE和代码编辑器', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 22:47:56', 6);
INSERT INTO `category` VALUES (8, '版本控制', 'Git等版本控制工具', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 1);
INSERT INTO `category` VALUES (9, '数据库工具', '数据库管理和设计工具', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 1);
INSERT INTO `category` VALUES (10, '调试工具', '程序调试和测试工具', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 1);
INSERT INTO `category` VALUES (11, '文档处理', 'Word、PDF等文档处理软件', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 2);
INSERT INTO `category` VALUES (12, '表格工具', 'Excel等电子表格软件', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 2);
INSERT INTO `category` VALUES (13, '演示文稿', 'PPT等演示文稿软件', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 2);
INSERT INTO `category` VALUES (14, '项目管理', '项目管理和协作工具', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 2);
INSERT INTO `category` VALUES (15, '图片编辑', 'Photoshop等图片编辑软件', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 3);
INSERT INTO `category` VALUES (16, '矢量设计', 'AI等矢量图形设计软件', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 3);
INSERT INTO `category` VALUES (17, '3D建模', '3D建模和渲染软件', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 3);
INSERT INTO `category` VALUES (18, '截图工具', '屏幕截图和录制工具', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 3);
INSERT INTO `category` VALUES (19, '系统优化', '系统清理和优化工具', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 4);
INSERT INTO `category` VALUES (20, '文件管理', '文件管理和压缩工具', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 4);
INSERT INTO `category` VALUES (21, '硬件检测', '硬件信息检测工具', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 4);
INSERT INTO `category` VALUES (22, '驱动程序', '驱动程序管理工具', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 4);
INSERT INTO `category` VALUES (23, '浏览器', '网页浏览器', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 5);
INSERT INTO `category` VALUES (24, '下载工具', '文件下载和传输工具', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 5);
INSERT INTO `category` VALUES (25, '网络监控', '网络监控和分析工具', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 5);
INSERT INTO `category` VALUES (26, '远程控制', '远程桌面和控制软件', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 5);
INSERT INTO `category` VALUES (27, '音频播放', '音乐播放器', NULL, 1, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 6);
INSERT INTO `category` VALUES (28, '视频播放', '视频播放器', NULL, 2, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 6);
INSERT INTO `category` VALUES (29, '游戏平台', '游戏启动器和平台', NULL, 3, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 6);
INSERT INTO `category` VALUES (30, '直播工具', '直播和录制软件', NULL, 4, 1, '2025-07-30 17:17:38', '2025-07-30 17:17:38', 6);
INSERT INTO `category` VALUES (33, '22', '1234124', NULL, 0, 1, '2025-07-30 22:56:38', '2025-07-30 22:56:38', 2);
INSERT INTO `category` VALUES (34, '1', '1124', NULL, 0, 1, '2025-07-30 23:00:59', '2025-07-30 23:00:59', NULL);
INSERT INTO `category` VALUES (35, '546', '62', NULL, 0, 1, '2025-07-30 23:01:11', '2025-07-30 23:01:11', 34);

-- ----------------------------
-- Table structure for site_config
-- ----------------------------
DROP TABLE IF EXISTS `site_config`;
CREATE TABLE `site_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `site_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '软件下载之家' COMMENT '网站名称',
  `site_logo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站LOGO地址',
  `site_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '网站描述',
  `site_keywords` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网站关键词',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `icp` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备案号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '网站配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of site_config
-- ----------------------------
INSERT INTO `site_config` VALUES (1, '软件下载之家', 'http://localhost:8088/api/uploads/images/设计软件下载网站 logo (1)_1753967655316.png', '专业的软件下载网站，提供安全、绿色、免费的软件下载服务', '软件下载,免费软件,绿色软件,安全下载', '<EMAIL>', '2', '2025-07-30 20:00:39', '2025-07-31 21:14:17');

-- ----------------------------
-- Table structure for software
-- ----------------------------
DROP TABLE IF EXISTS `software`;
CREATE TABLE `software`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '软件名称',
  `category_id` bigint(20) NULL DEFAULT NULL COMMENT '分类ID',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '软件图标URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '软件说明',
  `detail_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '详细介绍',
  `publisher` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '发布人',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '软件标签，逗号分隔',
  `screenshots` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '软件截图URLs，逗号分隔',
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '软件功能',
  `changelog` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '更新日志',
  `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '下载地址',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '软件文件路径',
  `file_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '软件文件名',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小（字节）',
  `download_count` int(11) NULL DEFAULT 0 COMMENT '下载次数',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：1-正常，0-下架',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_software_category_id`(`category_id`) USING BTREE,
  CONSTRAINT `fk_software_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '软件信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of software
-- ----------------------------
INSERT INTO `software` VALUES (1, 'Visual Studio Code', 7, 'http://localhost:8088/api/uploads/images/d9fc0980-a120-40d2-81d9-669ccf108193.jpg', '轻量级但功能强大的源代码编辑器', 'Visual Studio Code是微软开发的免费源代码编辑器，支持Windows、macOS和Linux。内置Git控制，支持调试，语法高亮，智能代码补全，代码重构和丰富的扩展功能。', 'Microsoft', '2024-01-15 10:00:00', 'IDE,编辑器,开发工具,免费,千万人', 'http://localhost:8088/api/uploads/images/f365441d-db4b-4296-ace5-247fcf9675e6.jpg,http://localhost:8088/api/uploads/images/5ebeb731-3f84-472d-9b6c-5c17b7553e97.jpg', '智能代码补全,内置Git支持,丰富的扩展市场,多语言支持,调试功能,主题定制124124124124', 'v1.85.0: 新增AI辅助编程功能\nv1.84.0: 改进性能和稳定性\nv1.83.0: 新增主题定制功能', 'https://code.visualstudio.com/Download', '/api/uploads/software/白菜Cousor无限续杯v1.exe', '白菜Cousor无限续杯v1.exe', 36783578, 7, 1, '2025-07-30 15:00:37', '2025-07-31 17:13:37');
INSERT INTO `software` VALUES (2, 'IntelliJ IDEA Community', 7, 'http://localhost:8088/api/uploads/images/11.png', '智能的Java集成开发环境', 'IntelliJ IDEA是JetBrains开发的Java IDE，提供智能代码辅助、强大的调试工具和丰富的框架支持。Community版本免费提供核心功能。', 'JetBrains', '2024-01-20 14:30:00', 'IDE,Java,开发工具,免费', 'http://localhost:8088/uploads/images/idea-screenshot1.png,http://localhost:8088/uploads/images/idea-screenshot2.png', '智能代码分析,强大的重构工具,版本控制集成,Maven/Gradle支持,调试器', 'v2024.1: 新增AI助手功能\nv2023.3: 改进Kotlin支持\nv2023.2: 性能优化', 'https://www.jetbrains.com/idea/download/', '/api/uploads/software/python-3.13.5-amd64 (2).exe', 'python-3.13.5-amd64 (2).exe', 28838672, 0, 1, '2025-07-30 15:00:37', '2025-07-31 10:48:32');
INSERT INTO `software` VALUES (3, 'Git for Windows', 8, '/uploads/images/git-icon.png', '分布式版本控制系统', 'Git是一个免费的开源分布式版本控制系统，旨在快速高效地处理从小型到大型项目的所有内容。包含Git Bash和Git GUI。', 'Git SCM', '2024-02-01 09:00:00', '版本控制,Git,开发工具,免费', '/uploads/images/git-screenshot1.png', '分布式版本控制,分支管理,合并工具,命令行界面,图形界面', 'v2.44.0: 改进性能\nv2.43.0: 新增功能\nv2.42.0: 安全更新', 'https://git-scm.com/download/win', NULL, 'Git-2.44.0-64-bit.exe', 47234567, 2, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (4, 'LibreOffice', 11, '/uploads/images/libreoffice-icon.png', '免费开源的办公套件', 'LibreOffice是一个功能强大的办公套件，包含文字处理、电子表格、演示文稿、绘图、数据库和数学公式编辑器。完全免费且开源。', 'The Document Foundation', '2024-01-25 11:00:00', '办公软件,文档处理,免费,开源', '/uploads/images/libreoffice-screenshot1.png,/uploads/images/libreoffice-screenshot2.png', '文字处理,电子表格,演示文稿,绘图工具,数据库,公式编辑', 'v7.6.4: 安全更新\nv7.6.3: 性能改进\nv7.6.2: 错误修复', 'https://www.libreoffice.org/download/', NULL, 'LibreOffice_7.6.4_Win_x86-64.msi', 312456789, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (5, 'Notepad++', 7, '/uploads/images/notepadpp-icon.png', '免费的源代码编辑器', 'Notepad++是一个免费的源代码编辑器和记事本替代品，支持多种编程语言。具有语法高亮、代码折叠、自动完成等功能。', 'Don Ho', '2024-02-10 16:30:00', '文本编辑器,代码编辑,免费,轻量级', '/uploads/images/notepadpp-screenshot1.png', '语法高亮,多标签页,查找替换,插件支持,宏录制', 'v8.6.2: 新增功能\nv8.6.1: 错误修复\nv8.6.0: 界面改进', 'https://notepad-plus-plus.org/downloads/', NULL, 'npp.8.6.2.Installer.x64.exe', 4234567, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (6, 'GIMP', 15, '/uploads/images/gimp-icon.png', '免费的图像编辑软件', 'GIMP是一个免费的开源图像编辑软件，提供专业级的图像处理功能，支持多种文件格式，具有丰富的滤镜和工具。', 'GIMP Team', '2024-01-30 14:00:00', '图像编辑,免费,开源,设计工具', '/uploads/images/gimp-screenshot1.png,/uploads/images/gimp-screenshot2.png', '图层编辑,滤镜效果,色彩调整,选择工具,画笔工具,插件支持', 'v2.10.36: 性能改进\nv2.10.35: 新增滤镜\nv2.10.34: 错误修复', 'https://www.gimp.org/downloads/', NULL, 'gimp-2.10.36-setup.exe', 234567890, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (7, 'Paint.NET', 15, '/uploads/images/paintnet-icon.png', '简单易用的图像编辑软件', 'Paint.NET是一个免费的图像和照片编辑软件，界面简洁易用，支持图层、特效、插件等功能，适合初学者和中级用户。', 'dotPDN LLC', '2024-02-05 10:30:00', '图像编辑,免费,简单易用', '/uploads/images/paintnet-screenshot1.png', '图层支持,特效滤镜,插件扩展,简洁界面,快速处理', 'v5.0.12: 性能优化\nv5.0.11: 新增功能\nv5.0.10: 错误修复', 'https://www.getpaint.net/download.html', NULL, 'paint.net.5.0.12.install.anycpu.web.exe', 12345678, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (8, '7-Zip', 20, '/uploads/images/7zip-icon.png', '免费的文件压缩工具', '7-Zip是一个免费的文件压缩软件，支持多种压缩格式，具有高压缩比和强大的加密功能。完全免费且开源。', 'Igor Pavlov', '2024-01-18 12:00:00', '压缩工具,免费,开源,文件管理', '/uploads/images/7zip-screenshot1.png', '多格式支持,高压缩率,AES-256加密,右键菜单集成,命令行支持', 'v23.01: 性能改进\nv22.01: 新增功能\nv21.07: 错误修复', 'https://www.7-zip.org/download.html', NULL, '7z2301-x64.exe', 1234567, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (9, 'CCleaner', 19, '/uploads/images/ccleaner-icon.png', '系统清理和优化工具', 'CCleaner是一个系统清理工具，可以清理临时文件、注册表、浏览器缓存等，帮助提升系统性能和释放磁盘空间。', 'Piriform', '2024-02-12 15:45:00', '系统清理,优化工具,性能提升', '/uploads/images/ccleaner-screenshot1.png', '垃圾文件清理,注册表清理,启动项管理,软件卸载,隐私保护', 'v6.19: 安全更新\nv6.18: 性能改进\nv6.17: 新增功能', 'https://www.ccleaner.com/ccleaner/download', NULL, 'ccsetup619.exe', 45678901, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (10, 'Mozilla Firefox', 23, '/uploads/images/firefox-icon.png', '开源的网络浏览器', 'Firefox是Mozilla开发的免费开源网络浏览器，注重隐私保护和用户自由，支持丰富的扩展和定制功能。', 'Mozilla', '2024-02-08 11:20:00', '浏览器,开源,隐私保护,免费', '/uploads/images/firefox-screenshot1.png,/uploads/images/firefox-screenshot2.png', '隐私保护,扩展支持,标签页管理,同步功能,开发者工具', 'v123.0: 性能提升\nv122.0: 安全更新\nv121.0: 新增功能', 'https://www.mozilla.org/firefox/download/', NULL, 'Firefox Setup 123.0.exe', 56789012, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (11, 'Thunderbird', 25, '/uploads/images/thunderbird-icon.png', '免费的邮件客户端', 'Thunderbird是Mozilla开发的免费邮件客户端，支持多账户管理、垃圾邮件过滤、扩展插件等功能。', 'Mozilla', '2024-02-15 09:30:00', '邮件客户端,免费,开源', '/uploads/images/thunderbird-screenshot1.png', '多账户支持,垃圾邮件过滤,扩展插件,日历集成,加密支持', 'v115.7: 安全更新\nv115.6: 性能改进\nv115.5: 错误修复', 'https://www.thunderbird.net/download/', NULL, 'Thunderbird Setup 115.7.0.exe', 67890123, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (12, 'VLC Media Player', 30, '/uploads/images/vlc-icon.png', '免费的多媒体播放器', 'VLC是一个免费的开源跨平台多媒体播放器，支持大多数多媒体文件以及DVD、音频CD、VCD和各种流媒体协议。', 'VideoLAN', '2024-02-18 13:15:00', '媒体播放器,免费,开源,多格式', '/uploads/images/vlc-screenshot1.png', '多格式支持,字幕支持,流媒体播放,音频均衡器,视频滤镜', 'v3.0.20: 安全更新\nv3.0.19: 性能改进\nv3.0.18: 错误修复', 'https://www.videolan.org/vlc/', NULL, 'vlc-3.0.20-win64.exe', 41234567, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (13, 'Malwarebytes', 19, '/uploads/images/malwarebytes-icon.png', '反恶意软件工具', 'Malwarebytes是一款专业的反恶意软件工具，能够检测和清除恶意软件、间谍软件、广告软件等威胁。', 'Malwarebytes', '2024-02-22 16:00:00', '安全软件,反恶意软件,系统保护', '/uploads/images/malwarebytes-screenshot1.png', '实时保护,恶意软件扫描,网页保护,勒索软件防护,隔离功能', 'v4.6.7: 新增功能\nv4.6.6: 性能改进\nv4.6.5: 错误修复', 'https://www.malwarebytes.com/mwb-download', NULL, 'MBSetup.exe', 78901234, 0, 1, '2025-07-30 15:00:37', '2025-07-30 17:17:38');
INSERT INTO `software` VALUES (14, '125', 33, 'http://localhost:8088/api/uploads/images/71e46354-1a5c-4130-baf1-48767cc64025.jpg', '1', '12', '125', '2025-07-30 15:37:54', '125,124,111,23', 'http://localhost:8088/api/uploads/images/e54b54f0-9fe1-4cc1-811b-0e0a8cce09a5.jpg', '12124\n124\n125\n', '412\n12412\n\n523523', '12124', '', '', NULL, 2, 1, '2025-07-30 23:38:03', '2025-07-31 18:05:22');
INSERT INTO `software` VALUES (15, '1241', 34, 'http://localhost:8088/api/uploads/images/设计软件下载网站 logo (1).png', '2', '124', '系统管理员', '2025-07-31 17:54:24', '124', 'http://localhost:8088/api/uploads/images/设计软件下载网站 logo (1)_1753964950154.png', '', '', 'https://www.douyin.com/?recommend=1', '/api/uploads/software/白菜Cousor无限续杯v1.exe', '白菜Cousor无限续杯v1.exe', 36783578, 3, 1, '2025-07-31 17:54:44', '2025-07-31 20:29:14');

SET FOREIGN_KEY_CHECKS = 1;
