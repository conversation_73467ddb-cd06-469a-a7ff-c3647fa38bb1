<template>
  <div class="software-manage">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd" icon="el-icon-plus">
        添加软件
      </el-button>
      <el-button @click="loadSoftwareList" icon="el-icon-refresh">
        刷新
      </el-button>
    </div>

    <!-- 软件列表 -->
    <el-table 
      :data="softwareList" 
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="软件图标" width="100">
        <template slot-scope="scope">
          <img
            :src="scope.row.icon"
            :alt="scope.row.name"
            class="software-icon clickable-image"
            @click="previewImage(scope.row.icon)"
          />
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="软件名称" min-width="150" />

      <el-table-column prop="description" label="软件说明" min-width="200" show-overflow-tooltip />

      <el-table-column label="分类" width="120">
        <template slot-scope="scope">
          {{ getCategoryName(scope.row.categoryId) }}
        </template>
      </el-table-column>

      <el-table-column prop="publisher" label="发布人" width="120" />
      
      <el-table-column label="发布时间" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.publishTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="downloadCount" label="下载次数" width="100" />

      <el-table-column prop="tags" label="软件标签" width="150" show-overflow-tooltip />

      <el-table-column label="文件信息" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.fileName">
            <div class="file-name">{{ scope.row.fileName }}</div>
            <div class="file-size">{{ formatFileSize(scope.row.fileSize) }}</div>
            <el-button
              size="mini"
              type="primary"
              @click="downloadFile(scope.row)"
              class="download-btn">
              <i class="el-icon-download"></i> 下载
            </el-button>
          </div>
          <span v-else>无文件</span>
        </template>
      </el-table-column>

      <el-table-column label="软件截图" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.screenshots" class="screenshot-preview">
            <img
              :src="getFirstScreenshot(scope.row.screenshots)"
              :alt="scope.row.name + '截图'"
              class="screenshot-thumb clickable-image"
              @click="previewScreenshots(scope.row.screenshots)"
            />
            <span class="screenshot-count">{{ getScreenshotCount(scope.row.screenshots) }}张</span>
          </div>
          <span v-else class="no-screenshot">无截图</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '正常' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            @click="handleEdit(scope.row)"
            icon="el-icon-edit"
          >
            编辑
          </el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="handleDelete(scope.row)"
            icon="el-icon-delete"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form 
        ref="softwareForm" 
        :model="softwareForm" 
        :rules="softwareRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="软件名称" prop="name">
              <el-input v-model="softwareForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="softwareForm.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option
                  v-for="category in categoryList"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发布人" prop="publisher">
              <el-input v-model="softwareForm.publisher" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker
                v-model="softwareForm.publishTime"
                type="datetime"
                placeholder="选择发布时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="软件图标" prop="icon">
          <el-upload
            class="icon-uploader"
            :action="uploadUrl + '/admin/upload/image'"
            :show-file-list="false"
            :on-success="handleIconSuccess"
            :before-upload="beforeIconUpload">
            <img v-if="softwareForm.icon" :src="softwareForm.icon" class="icon-preview">
            <i v-else class="el-icon-plus icon-uploader-icon"></i>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png文件，且不超过2MB</div>
        </el-form-item>
        
        <el-form-item label="软件说明" prop="description">
          <el-input 
            v-model="softwareForm.description" 
            type="textarea" 
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="详细介绍" prop="detailDescription">
          <el-input 
            v-model="softwareForm.detailDescription" 
            type="textarea" 
            :rows="4"
          />
        </el-form-item>
        
        <el-form-item label="软件标签" prop="tags">
          <div class="tag-input-wrapper">
            <div class="tag-input-box" :class="{ focused: tagInputFocused }" @click="focusInput">
              <!-- 显示已添加的标签 -->
              <el-tag
                v-for="tag in tagList"
                :key="tag"
                size="small"
                closable
                @close="removeTag(tag)"
                class="tag-item">
                {{ tag }}
              </el-tag>
              <!-- 小的输入框 -->
              <el-input
                ref="tagInput"
                v-model="tagInputValue"
                size="small"
                class="small-tag-input"
                placeholder="输入标签后按回车"
                @keyup.enter.native="addTag"
                @focus="onInputFocus"
                @blur="onInputBlur">
              </el-input>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="下载地址" prop="downloadUrl">
          <el-input v-model="softwareForm.downloadUrl" placeholder="请输入软件下载地址" />
        </el-form-item>
        
        <el-form-item label="软件截图" prop="screenshots">
          <el-upload
            class="screenshot-uploader"
            :action="uploadUrl + '/admin/upload/image'"
            :file-list="screenshotList"
            :on-success="handleScreenshotSuccess"
            :on-remove="handleScreenshotRemove"
            :before-upload="beforeScreenshotUpload"
            list-type="picture-card"
            multiple>
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">可上传多张截图，支持jpg/png格式，单张不超过5MB</div>
        </el-form-item>

        <el-form-item label="软件文件" prop="filePath">
          <el-upload
            class="file-uploader"
            :action="uploadUrl + '/admin/upload/software'"
            :show-file-list="false"
            :on-success="handleFileSuccess"
            :before-upload="beforeFileUpload">
            <el-button size="small" type="primary">
              <i class="el-icon-upload el-icon--left"></i>
              {{ softwareForm.fileName || '选择软件文件' }}
            </el-button>
          </el-upload>
          <div class="upload-tip">支持exe、zip、rar、7z、msi等格式，最大100MB</div>
          <div v-if="softwareForm.fileSize" class="file-info">
            文件大小: {{ formatFileSize(softwareForm.fileSize) }}
          </div>
        </el-form-item>
        
        <el-form-item label="软件功能" prop="features">
          <el-input 
            v-model="softwareForm.features" 
            type="textarea" 
            :rows="3"
            placeholder="多个功能用逗号分隔"
          />
        </el-form-item>
        
        <el-form-item label="更新日志" prop="changelog">
          <el-input 
            v-model="softwareForm.changelog" 
            type="textarea" 
            :rows="4"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="softwareForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ submitLoading ? '保存中...' : '确定' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 全屏图片预览 -->
    <div v-if="previewVisible" class="fullscreen-image-preview" @click="closePreview">
      <!-- 左右切换箭头 -->
      <div v-if="previewImages.length > 1" class="preview-nav-arrows">
        <button
          @click.stop="prevImage"
          class="nav-arrow nav-arrow-left"
          :disabled="currentImageIndex === 0"
          title="上一张">
          <i class="el-icon-arrow-left"></i>
        </button>
        <button
          @click.stop="nextImage"
          class="nav-arrow nav-arrow-right"
          :disabled="currentImageIndex === previewImages.length - 1"
          title="下一张">
          <i class="el-icon-arrow-right"></i>
        </button>
      </div>

      <!-- 图片计数器 -->
      <div v-if="previewImages.length > 1" class="image-counter">
        {{ currentImageIndex + 1 }} / {{ previewImages.length }}
      </div>

      <div class="preview-controls-bottom">
        <div class="control-buttons">
          <button @click.stop="zoomOut" class="control-btn" title="缩小">
            <i class="el-icon-zoom-out"></i>
          </button>
          <button @click.stop="zoomIn" class="control-btn" title="放大">
            <i class="el-icon-zoom-in"></i>
          </button>
          <button @click.stop="resetZoom" class="control-btn" title="重置">
            <i class="el-icon-refresh"></i>
          </button>
          <button @click.stop="rotateLeft" class="control-btn" title="左转">
            <i class="el-icon-refresh-left"></i>
          </button>
          <button @click.stop="rotateRight" class="control-btn" title="右转">
            <i class="el-icon-refresh-right"></i>
          </button>
          <button @click.stop="closePreview" class="control-btn close-btn" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>
      <div class="fullscreen-image-container" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag" @mouseleave="endDrag" @wheel="onWheel">
        <img
          :src="previewImageUrl"
          class="fullscreen-preview-image"
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
        />
      </div>
    </div>
  </div>
</template>

<script>
import { adminApi } from '@/api'
import { formatDate } from '@/utils'

export default {
  name: 'SoftwareManage',
  data() {
    return {
      softwareList: [],
      categoryList: [],
      loading: false,
      dialogVisible: false,
      dialogTitle: '添加软件',
      isEdit: false,
      submitLoading: false,
      uploadUrl: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8088/api',
      screenshotList: [],

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 图片预览相关
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },

      // 多张截图预览相关
      previewImages: [],
      currentImageIndex: 0,

      // 标签相关
      tagList: [],
      tagInputValue: '',
      tagInputFocused: false,





      softwareForm: {
        id: null,
        name: '',
        categoryId: null,
        icon: '',
        description: '',
        detailDescription: '',
        publisher: '',
        publishTime: null,
        tags: '',
        screenshots: '',
        filePath: '',
        fileName: '',
        fileSize: null,
        features: '',
        changelog: '',
        downloadUrl: '',
        status: 1
      },
      
      softwareRules: {
        name: [
          { required: true, message: '请输入软件名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        publisher: [
          { required: true, message: '请输入发布人', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入软件说明', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 图片样式
    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    }
  },
  watch: {
    // 监听标签列表变化，同步到表单
    tagList: {
      handler(newTags) {
        this.softwareForm.tags = newTags.join(',')
      },
      deep: true
    }
  },
  created() {
    this.loadSoftwareList()
    this.loadCategoryList()

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)
  },

  beforeDestroy() {
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    formatDate,
    
    async loadSoftwareList() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize
        }
        const res = await adminApi.getSoftwareList(params)
        console.log('软件列表响应:', res)
        // 后端返回的是分页格式 {list: [], total: 0, page: 1, size: 10}
        if (res.data && res.data.list) {
          this.softwareList = res.data.list
          this.total = res.data.total || 0
        } else {
          this.softwareList = []
          this.total = 0
        }
        console.log('软件列表数据:', this.softwareList)
      } catch (error) {
        console.error('加载软件列表失败:', error)
        this.$message.error('加载软件列表失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },

    async loadCategoryList() {
      try {
        const response = await adminApi.getCategoryList()
        if (response.code === 200) {
          this.categoryList = response.data
        }
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    },

    getCategoryName(categoryId) {
      const category = this.categoryList.find(c => c.id === categoryId)
      return category ? category.name : '未分类'
    },
    
    handleAdd() {
      this.dialogTitle = '添加软件'
      this.isEdit = false
      this.resetForm()
      // 自动设置发布人为当前登录用户
      this.softwareForm.publisher = this.getCurrentUser()
      this.dialogVisible = true
    },
    
    handleEdit(row) {
      this.dialogTitle = '编辑软件'
      this.isEdit = true
      this.softwareForm = { ...row }

      // 处理标签：将逗号分隔的字符串转换为数组
      this.tagList = row.tags ? row.tags.split(',').filter(tag => tag.trim()) : []
      this.tagInputValue = ''

      // 处理截图显示：将数据库中的截图URL字符串转换为文件列表
      this.screenshotList = []
      if (row.screenshots) {
        const screenshotUrls = row.screenshots.split(',').filter(url => url.trim())
        this.screenshotList = screenshotUrls.map((url, index) => ({
          name: `screenshot-${index + 1}`,
          url: url.startsWith('http') ? url : `http://localhost:8088${url}`,
          uid: Date.now() + index
        }))
      }

      this.dialogVisible = true
    },
    
    handleDelete(row) {
      this.$confirm(`确定要删除软件"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await adminApi.deleteSoftware(row.id)
          this.$message.success('删除成功')
          this.loadSoftwareList()
        } catch (error) {
          console.error('删除失败:', error)
        }
      })
    },
    
    handleSubmit() {
      this.$refs.softwareForm.validate(async (valid) => {
        if (!valid) return
        
        this.submitLoading = true
        try {
          if (this.isEdit) {
            await adminApi.updateSoftware(this.softwareForm.id, this.softwareForm)
            this.$message.success('更新成功')
          } else {
            await adminApi.addSoftware(this.softwareForm)
            this.$message.success('添加成功')
          }
          
          this.dialogVisible = false
          this.loadSoftwareList()
        } catch (error) {
          console.error('保存失败:', error)
        } finally {
          this.submitLoading = false
        }
      })
    },

    // 图标上传成功处理
    handleIconSuccess(response) {
      if (response.code === 200) {
        let iconUrl = response.data.url
        if (iconUrl && !iconUrl.startsWith('http')) {
          iconUrl = 'http://localhost:8088' + iconUrl
        }
        this.softwareForm.icon = iconUrl
        this.$message.success('图标上传成功')
      } else {
        this.$message.error(response.message || '图标上传失败')
      }
    },

    // 图标上传前验证
    beforeIconUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    // 截图上传成功处理
    handleScreenshotSuccess(response, file, fileList) {
      if (response.code === 200) {
        const urls = fileList.map(f => {
          let url = f.response ? f.response.data.url : f.url
          if (url && !url.startsWith('http')) {
            url = 'http://localhost:8088' + url
          }
          return url
        }).filter(Boolean)
        this.softwareForm.screenshots = urls.join(',')
        this.$message.success('截图上传成功')
      } else {
        this.$message.error(response.message || '截图上传失败')
      }
    },

    // 截图移除处理
    handleScreenshotRemove(file, fileList) {
      const urls = fileList.map(f => {
        let url = f.response ? f.response.data.url : f.url
        if (url && !url.startsWith('http')) {
          url = 'http://localhost:8088' + url
        }
        return url
      }).filter(Boolean)
      this.softwareForm.screenshots = urls.join(',')
    },

    // 截图上传前验证
    beforeScreenshotUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      return true
    },

    // 软件文件上传成功处理
    handleFileSuccess(response) {
      if (response.code === 200) {
        this.softwareForm.filePath = response.data.filePath
        this.softwareForm.fileName = response.data.fileName
        this.softwareForm.fileSize = response.data.fileSize
        this.$message.success('软件文件上传成功')
      } else {
        this.$message.error(response.message || '软件文件上传失败')
      }
    },

    // 软件文件上传前验证
    beforeFileUpload(file) {
      const allowedTypes = ['.exe', '.zip', '.rar', '.7z', '.msi', '.dmg', '.pkg', '.deb', '.rpm']
      const fileName = file.name.toLowerCase()
      const isAllowed = allowedTypes.some(type => fileName.endsWith(type))
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isAllowed) {
        this.$message.error('只能上传软件安装包文件!')
        return false
      }
      if (!isLt100M) {
        this.$message.error('文件大小不能超过 100MB!')
        return false
      }
      return true
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    resetForm() {
      this.softwareForm = {
        id: null,
        name: '',
        categoryId: null,
        icon: '',
        description: '',
        detailDescription: '',
        publisher: this.getCurrentUser(),
        publishTime: null,
        tags: '',
        screenshots: '',
        features: '',
        changelog: '',
        downloadUrl: '',
        filePath: '',
        fileName: '',
        fileSize: null,
        status: 1
      }
      this.screenshotList = []
      this.tagList = []
      this.tagInputValue = ''
      this.tagInputFocused = false
      this.$nextTick(() => {
        this.$refs.softwareForm && this.$refs.softwareForm.clearValidate()
      })
    },

    // 分页处理方法
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadSoftwareList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadSoftwareList()
    },

    // 获取第一张截图
    getFirstScreenshot(screenshots) {
      if (!screenshots) return ''
      const urls = screenshots.split(',').filter(url => url.trim())
      if (urls.length > 0) {
        let url = urls[0].trim()
        if (url && !url.startsWith('http')) {
          url = `http://localhost:8088${url.startsWith('/') ? '' : '/'}${url}`
        }
        return url
      }
      return ''
    },

    // 获取截图数量
    getScreenshotCount(screenshots) {
      if (!screenshots) return 0
      return screenshots.split(',').filter(url => url.trim()).length
    },

    // 预览单张图片
    previewImage(url) {
      if (!url) return
      console.log('预览图片URL:', url)
      // 确保URL格式正确
      let imageUrl = url
      if (imageUrl && !imageUrl.startsWith('http')) {
        imageUrl = `http://localhost:8088${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`
      }
      console.log('处理后的图片URL:', imageUrl)

      // 设置为单张图片预览
      this.previewImages = [imageUrl]
      this.currentImageIndex = 0
      this.previewImageUrl = imageUrl
      this.previewVisible = true
      this.resetImageState()
    },

    // 预览截图（支持多张切换）
    previewScreenshots(screenshots) {
      if (!screenshots) return

      // 解析所有截图URL
      const urls = screenshots.split(',').filter(url => url.trim()).map(url => {
        let imageUrl = url.trim()
        if (imageUrl && !imageUrl.startsWith('http')) {
          imageUrl = `http://localhost:8088${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`
        }
        return imageUrl
      })

      if (urls.length > 0) {
        this.previewImages = urls
        this.currentImageIndex = 0
        this.previewImageUrl = urls[0]
        this.previewVisible = true
        this.resetImageState()
      }
    },

    // 重置图片状态
    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    // 放大
    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    // 缩小
    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    // 重置缩放
    resetZoom() {
      this.resetImageState()
    },

    // 左转
    rotateLeft() {
      this.imageRotation -= 90
    },

    // 右转
    rotateRight() {
      this.imageRotation += 90
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    // 拖拽中
    onDrag(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
      }
    },

    // 结束拖拽
    endDrag() {
      this.isDragging = false
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.resetImageState()
    },

    // 鼠标滚轮缩放
    onWheel(event) {
      event.preventDefault()
      if (event.deltaY < 0) {
        this.zoomIn()
      } else {
        this.zoomOut()
      }
    },

    // 上一张图片
    prevImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
        this.previewImageUrl = this.previewImages[this.currentImageIndex]
        this.resetImageState()
      }
    },

    // 下一张图片
    nextImage() {
      if (this.currentImageIndex < this.previewImages.length - 1) {
        this.currentImageIndex++
        this.previewImageUrl = this.previewImages[this.currentImageIndex]
        this.resetImageState()
      }
    },

    // 键盘事件处理
    handleKeydown(event) {
      if (!this.previewVisible) return

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          this.prevImage()
          break
        case 'ArrowRight':
          event.preventDefault()
          this.nextImage()
          break
        case 'Escape':
          event.preventDefault()
          this.closePreview()
          break
      }
    },

    // 获取当前登录用户
    getCurrentUser() {
      // 从localStorage获取管理员信息
      const adminInfo = localStorage.getItem('admin')
      if (adminInfo) {
        try {
          const admin = JSON.parse(adminInfo)
          return admin.nickname || admin.username || '管理员'
        } catch (error) {
          console.error('解析管理员信息失败:', error)
        }
      }
      return '管理员'
    },

    // 标签处理方法
    addTag() {
      const tag = this.tagInputValue.trim()
      if (tag && !this.tagList.includes(tag)) {
        this.tagList.push(tag)
        this.tagInputValue = ''
      }
    },

    removeTag(tag) {
      this.tagList = this.tagList.filter(t => t !== tag)
    },

    // 聚焦输入框
    focusInput() {
      this.$refs.tagInput.focus()
    },

    // 输入框聚焦事件
    onInputFocus() {
      this.tagInputFocused = true
    },

    // 输入框失焦事件
    onInputBlur() {
      this.tagInputFocused = false
      this.addTag() // 失焦时也添加标签
    },

    // 文件下载方法
    downloadFile(row) {
      if (!row.filePath) {
        this.$message.warning('该软件没有上传文件')
        return
      }

      try {
        // 构建下载URL
        const downloadUrl = `http://localhost:8088${row.filePath.startsWith('/') ? '' : '/'}${row.filePath}`

        // 创建隐藏的下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = row.fileName || '软件文件'
        link.style.display = 'none'

        // 添加到DOM并触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)

        this.$message.success('开始下载文件')
      } catch (error) {
        console.error('下载文件失败:', error)
        this.$message.error('下载文件失败')
      }
    }
  }
}
</script>

<style scoped>
.software-manage {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.toolbar {
  margin-bottom: 20px;
}

.software-icon {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 18px;
}

/* 图标上传样式 */
.icon-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.icon-uploader .el-upload:hover {
  border-color: #409EFF;
}

.icon-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.icon-preview {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}

/* 截图上传样式 */
.screenshot-uploader .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.screenshot-uploader .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

/* 文件上传样式 */
.file-uploader {
  margin-bottom: 10px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.file-info {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 可点击图片样式 */
.clickable-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable-image:hover {
  transform: scale(1.1);
}

/* 截图预览样式 */
.screenshot-preview {
  position: relative;
  display: inline-block;
}

.screenshot-thumb {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.screenshot-count {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: #409EFF;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
  line-height: 1;
}

.no-screenshot {
  color: #999;
  font-size: 12px;
}

/* 文件信息样式 */
.file-name {
  font-size: 12px;
  color: #333;
  margin-bottom: 2px;
  word-break: break-all;
}

.file-size {
  font-size: 11px;
  color: #666;
  margin-bottom: 5px;
}

.download-btn {
  font-size: 11px;
  padding: 2px 6px;
}

/* 标签输入容器样式 */
.tag-input-wrapper {
  width: 100%;
}

.tag-input-box {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
  min-height: 100px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  cursor: text;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.tag-input-box:hover {
  border-color: #c0c4cc;
}

.tag-input-box.focused {
  border-color: #409eff;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.tag-item {
  margin: 0;
  flex-shrink: 0;
  font-size: 12px;
  height: 24px;
  line-height: 22px;
}

/* 小的标签输入框 */
.small-tag-input {
  width: 200px;
  margin-top: 4px;
}

.small-tag-input .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
  border: 1px solid #e4e7ed;
  border-radius: 3px;
  padding: 0 8px;
}

.small-tag-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}



/* 全屏图片预览样式 */
.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
}

.fullscreen-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  user-select: none;
}

.fullscreen-preview-image {
  max-width: 95vw;
  max-height: 95vh;
  width: auto;
  height: auto;
  object-fit: contain;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transform-origin: center center;
  cursor: grab;
}

.fullscreen-preview-image:active {
  cursor: grabbing;
}

/* 左右切换箭头 */
.preview-nav-arrows {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  z-index: 100001;
  pointer-events: none;
}

.nav-arrow {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.nav-arrow:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-arrow-left {
  left: 30px;
}

.nav-arrow-right {
  right: 30px;
}

/* 图片计数器 */
.image-counter {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 100001;
}

/* 底部控制栏 */
.preview-controls-bottom {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100000;
}

.control-buttons {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 25px;
  padding: 15px 20px;
  gap: 15px;
}

.control-btn {
  width: 45px;
  height: 45px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.close-btn {
  background: rgba(255, 59, 48, 0.8);
}

.control-btn.close-btn:hover {
  background: rgba(255, 59, 48, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-controls-bottom {
    bottom: 20px;
  }

  .control-buttons {
    padding: 12px 16px;
    gap: 12px;
  }

  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .fullscreen-preview-image {
    max-width: 98vw;
    max-height: 90vh;
  }
}
</style>
