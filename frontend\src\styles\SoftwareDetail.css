.software-detail {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 网站头部 */
.site-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 15px 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
}

.logo img {
  width: 40px;
  height: 40px;
}

.search-area {
  flex: 1;
  max-width: 500px;
  margin: 0 50px;
}

.search-input {
  width: 100%;
}

/* 主导航 */
.main-nav {
  background: #4a90e2;
  border-bottom: 1px solid #357abd;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item a {
  display: block;
  padding: 15px 20px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav-item a:hover,
.nav-item.active a {
  background-color: #357abd;
}

/* 主体内容 */
.main-content {
  padding: 30px 0;
}

.content-wrapper {
  display: flex;
  gap: 30px;
}

/* 左侧内容 */
.content-main {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 面包屑导航 */
.breadcrumb {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.breadcrumb a {
  color: #4a90e2;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

/* 软件详情 */
.software-header {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.software-icon {
  flex-shrink: 0;
}

.software-icon img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #eee;
}

.software-info {
  flex: 1;
}

.software-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.software-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.software-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.software-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.software-actions {
  display: flex;
  gap: 15px;
}

/* 软件详细信息 */
.software-details {
  margin-top: 30px;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #4a90e2;
}

.detail-content {
  color: #666;
  line-height: 1.8;
  white-space: pre-wrap;
}

/* 软件截图 */
.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.screenshot-item {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s;
}

.screenshot-item:hover {
  transform: scale(1.05);
}

.screenshot-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border: 1px solid #eee;
}

/* 右侧边栏 */
.sidebar-right {
  width: 300px;
  flex-shrink: 0;
}

.widget {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.widget-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  margin-bottom: 8px;
}

.category-list a {
  display: block;
  padding: 8px 12px;
  color: #666;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s;
}

.category-list a:hover,
.category-list a.active {
  background-color: #f0f8ff;
  color: #4a90e2;
}

/* 网站底部 */
.site-footer {
  background: #333;
  color: white;
  padding: 40px 0 20px;
  margin-top: 50px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.footer-info {
  flex: 1;
}

.site-desc {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 15px;
}

.footer-links {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-links span {
  color: #ccc;
  font-size: 14px;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #555;
  color: #999;
  font-size: 14px;
}

/* 图片预览 */
.image-preview-container {
  text-align: center;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  cursor: move;
  transition: transform 0.3s ease;
}

.zoom-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
}

.zoom-info {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 15px;
  }

  .search-area {
    margin: 0;
    max-width: 100%;
  }

  .nav-list {
    flex-wrap: wrap;
  }

  .nav-item a {
    padding: 12px 15px;
    font-size: 14px;
  }

  .content-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .sidebar-right {
    width: 100%;
  }

  .software-header {
    flex-direction: column;
    text-align: center;
  }

  .software-icon {
    align-self: center;
  }

  .software-meta {
    justify-content: center;
  }

  .software-actions {
    justify-content: center;
  }

  .screenshots-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
  }
}
