# 操作记录功能说明

## 概述
本系统已为所有重要的管理员操作添加了操作记录功能，所有操作都会自动记录到 `operation_log` 表中。

## 已添加操作记录的功能

### 1. 用户认证模块 (PROFILE)

#### 1.1 管理员登录
- **接口**: `POST /admin/login`
- **操作类型**: LOGIN
- **记录内容**: 管理员登录系统
- **记录信息**: IP地址、用户代理、登录时间

#### 1.2 修改密码
- **接口**: `POST /admin/change-password`
- **操作类型**: UPDATE
- **记录内容**: 修改登录密码
- **记录信息**: IP地址、用户代理、操作时间

#### 1.3 更新个人信息
- **接口**: `POST /admin/update-profile`
- **操作类型**: UPDATE
- **记录内容**: 更新个人信息
- **记录信息**: 管理员昵称、IP地址、用户代理

### 2. 软件管理模块 (SOFTWARE)

#### 2.1 添加软件
- **接口**: `POST /admin/software`
- **操作类型**: ADD
- **记录内容**: 添加软件：{软件名称}
- **记录信息**: 软件ID、软件名称、IP地址、用户代理

#### 2.2 修改软件
- **接口**: `PUT /admin/software/{id}`
- **操作类型**: UPDATE
- **记录内容**: 修改软件：{软件名称}
- **记录信息**: 软件ID、软件名称、IP地址、用户代理

#### 2.3 删除软件
- **接口**: `DELETE /admin/software/{id}`
- **操作类型**: DELETE
- **记录内容**: 删除软件：{软件名称}
- **记录信息**: 软件ID、软件名称、IP地址、用户代理

### 3. 分类管理模块 (CATEGORY)

#### 3.1 添加分类
- **接口**: `POST /admin/category/add`
- **操作类型**: ADD
- **记录内容**: 添加分类：{分类名称}
- **记录信息**: 分类ID、分类名称、IP地址、用户代理

#### 3.2 修改分类
- **接口**: `POST /admin/category/update`
- **操作类型**: UPDATE
- **记录内容**: 修改分类：{分类名称}
- **记录信息**: 分类ID、分类名称、IP地址、用户代理

#### 3.3 删除分类
- **接口**: `POST /admin/category/delete/{id}`
- **操作类型**: DELETE
- **记录内容**: 删除分类：{分类名称}
- **记录信息**: 分类ID、分类名称、IP地址、用户代理

### 4. 网站配置模块 (SITE_CONFIG)

#### 4.1 保存网站配置
- **接口**: `POST /admin/site-config`
- **操作类型**: UPDATE
- **记录内容**: 保存网站配置
- **记录信息**: 配置ID、IP地址、用户代理

## 操作记录字段说明

### 基础信息
- `admin_id`: 操作管理员ID
- `admin_username`: 操作管理员用户名
- `operation_type`: 操作类型（ADD/UPDATE/DELETE/LOGIN/LOGOUT）
- `operation_module`: 操作模块（SOFTWARE/CATEGORY/SITE_CONFIG/PROFILE）
- `operation_desc`: 操作描述
- `create_time`: 操作时间

### 目标信息
- `target_id`: 操作目标ID（如软件ID、分类ID等）
- `target_name`: 操作目标名称

### 请求信息
- `request_method`: 请求方法（GET/POST/PUT/DELETE）
- `request_url`: 请求URL
- `ip_address`: 客户端IP地址
- `user_agent`: 用户代理信息

### 状态信息
- `operation_status`: 操作状态（1-成功，0-失败）
- `error_message`: 错误信息（失败时记录）

## 查看操作记录

### 1. 个人信息页面
- 显示当前管理员最近10条操作记录
- 包含操作类型、模块、描述、IP地址、时间等信息

### 2. API接口
- `GET /admin/operation-logs`: 获取操作记录列表（分页）
- `GET /admin/operation-logs/recent`: 获取最近操作记录

## 注意事项

### 1. 管理员信息
- 当前使用固定的管理员ID（1L）和用户名（"admin"）
- 后续可以通过JWT token或session获取真实的管理员信息

### 2. IP地址获取
- 支持代理服务器环境（X-Forwarded-For、X-Real-IP）
- 自动获取客户端真实IP地址

### 3. 数据维护
- 建议定期清理旧的操作记录（可使用 `deleteOldRecords` 方法）
- 数据库已建立相关索引，查询性能良好

### 4. 扩展性
- 可以轻松添加新的操作类型和模块
- 支持记录任意的请求参数和错误信息

## 使用示例

```java
// 记录操作日志的标准方式
operationLogService.recordOperation(
    adminId,                              // 管理员ID
    adminUsername,                        // 管理员用户名
    OperationLog.OPERATION_TYPE_ADD,      // 操作类型
    OperationLog.OPERATION_MODULE_SOFTWARE, // 操作模块
    "添加软件：" + softwareName,           // 操作描述
    softwareId,                           // 目标ID
    softwareName,                         // 目标名称
    "POST",                               // 请求方法
    "/admin/software",                    // 请求URL
    null,                                 // 请求参数
    getClientIpAddress(request),          // IP地址
    request.getHeader("User-Agent")       // 用户代理
);
```

## 数据库初始化

执行以下SQL文件来创建操作记录表：
```sql
source backend/db/add_operation_log_table.sql;
```

该脚本会创建 `operation_log` 表并插入一些示例数据。
