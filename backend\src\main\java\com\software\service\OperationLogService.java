package com.software.service;

import com.software.entity.OperationLog;

import java.util.List;
import java.util.Map;

/**
 * 操作记录服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface OperationLogService {
    
    /**
     * 记录操作日志
     */
    void recordOperation(OperationLog operationLog);
    
    /**
     * 记录操作日志（简化版本）
     */
    void recordOperation(Long adminId, String adminUsername, String operationType, 
                        String operationModule, String operationDesc, 
                        Long targetId, String targetName);
    
    /**
     * 记录操作日志（包含请求信息）
     */
    void recordOperation(Long adminId, String adminUsername, String operationType, 
                        String operationModule, String operationDesc, 
                        Long targetId, String targetName, String requestMethod, 
                        String requestUrl, String requestParams, String ipAddress, 
                        String userAgent);
    
    /**
     * 获取管理员的操作记录（分页）
     */
    Map<String, Object> getOperationLogsByAdminId(Long adminId, int page, int size);
    
    /**
     * 获取管理员最近的操作记录
     */
    List<OperationLog> getRecentOperationLogs(Long adminId, int limit);
    
    /**
     * 根据操作类型获取操作记录
     */
    Map<String, Object> getOperationLogsByType(Long adminId, String operationType, int page, int size);
    
    /**
     * 根据操作模块获取操作记录
     */
    Map<String, Object> getOperationLogsByModule(Long adminId, String operationModule, int page, int size);

    /**
     * 根据条件获取操作记录（支持多条件筛选）
     */
    Map<String, Object> getOperationLogsByCondition(Long adminId, String operationType, String operationModule, int page, int size);

    /**
     * 清理旧的操作记录
     */
    int cleanOldRecords(int days);
    
    /**
     * 获取所有操作记录（超级管理员查看）
     */
    Map<String, Object> getAllOperationLogs(int page, int size);
}
