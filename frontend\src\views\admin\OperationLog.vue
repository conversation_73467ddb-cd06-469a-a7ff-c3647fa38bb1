<template>
  <div class="operation-log">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">操作记录</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="loadOperationLogs"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
      
      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="操作类型">
            <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="登录" value="LOGIN" />
              <el-option label="新增" value="ADD" />
              <el-option label="修改" value="UPDATE" />
              <el-option label="删除" value="DELETE" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="操作模块">
            <el-select v-model="searchForm.operationModule" placeholder="请选择操作模块" clearable>
              <el-option label="全部" value="" />
              <el-option label="个人信息" value="PROFILE" />
              <el-option label="软件管理" value="SOFTWARE" />
              <el-option label="分类管理" value="CATEGORY" />
              <el-option label="网站配置" value="SITE_CONFIG" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 操作记录表格 -->
      <el-table 
        :data="operationLogs" 
        style="width: 100%" 
        v-loading="loading"
        stripe
      >
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getOperationTypeColor(scope.row.operationType)" size="small">
              {{ getOperationTypeName(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="operationModule" label="操作模块" width="120">
          <template slot-scope="scope">
            <el-tag type="info" size="small">
              {{ getOperationModuleName(scope.row.operationModule) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="operationDesc" label="操作描述" min-width="180" />

        <el-table-column prop="adminUsername" label="操作人" width="120">
          <template slot-scope="scope">
            <el-tag type="success" size="small">
              {{ scope.row.adminUsername }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作对象" width="140">
          <template slot-scope="scope">
            <div v-if="scope.row.targetName" class="operation-target">
              <el-tag size="mini" :type="getTargetTagType(scope.row.operationModule)">
                {{ getTargetPrefix(scope.row.operationModule) }}
              </el-tag>
              <span class="target-name">{{ scope.row.targetName }}</span>
            </div>
            <span v-else class="text-muted">系统操作</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        
        <el-table-column prop="requestMethod" label="请求方法" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getMethodColor(scope.row.requestMethod)" 
              size="mini"
            >
              {{ scope.row.requestMethod }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="operationStatus" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.operationStatus === 1 ? 'success' : 'danger'" 
              size="small"
            >
              {{ scope.row.operationStatus === 1 ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminApi } from '@/api'

export default {
  name: 'OperationLog',
  data() {
    return {
      loading: false,
      operationLogs: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      searchForm: {
        operationType: '',
        operationModule: ''
      }
    }
  },
  created() {
    this.loadOperationLogs()
  },
  methods: {
    // 加载操作记录
    async loadOperationLogs() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          ...this.searchForm
        }
        const response = await adminApi.getOperationLogs(params)
        if (response.code === 200) {
          this.operationLogs = response.data.list || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载操作记录失败:', error)
        this.$message.error('加载操作记录失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadOperationLogs()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        operationType: '',
        operationModule: ''
      }
      this.currentPage = 1
      this.loadOperationLogs()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadOperationLogs()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadOperationLogs()
    },

    // 获取操作类型名称
    getOperationTypeName(type) {
      const typeMap = {
        'ADD': '新增',
        'UPDATE': '修改',
        'DELETE': '删除',
        'LOGIN': '登录',
        'LOGOUT': '登出'
      }
      return typeMap[type] || type
    },

    // 获取操作类型颜色
    getOperationTypeColor(type) {
      const colorMap = {
        'ADD': 'success',
        'UPDATE': 'warning',
        'DELETE': 'danger',
        'LOGIN': 'primary',
        'LOGOUT': 'info'
      }
      return colorMap[type] || 'info'
    },

    // 获取操作模块名称
    getOperationModuleName(module) {
      const moduleMap = {
        'SOFTWARE': '软件管理',
        'CATEGORY': '分类管理',
        'SITE_CONFIG': '网站配置',
        'PROFILE': '个人信息'
      }
      return moduleMap[module] || module
    },

    // 获取请求方法颜色
    getMethodColor(method) {
      const colorMap = {
        'GET': 'info',
        'POST': 'success',
        'PUT': 'warning',
        'DELETE': 'danger'
      }
      return colorMap[method] || 'info'
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '暂无记录'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    // 获取操作对象标签类型
    getTargetTagType(module) {
      const typeMap = {
        'SOFTWARE': 'primary',
        'CATEGORY': 'warning',
        'SITE_CONFIG': 'success',
        'PROFILE': 'info'
      }
      return typeMap[module] || 'info'
    },

    // 获取操作对象前缀
    getTargetPrefix(module) {
      const prefixMap = {
        'SOFTWARE': '软件',
        'CATEGORY': '分类',
        'SITE_CONFIG': '配置',
        'PROFILE': '用户'
      }
      return prefixMap[module] || '对象'
    }
  }
}
</script>

<style scoped>
.operation-log {
  padding: 0;
  background: white;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.box-card {
  border-radius: 0;
  border: none;
  box-shadow: none;
}

.box-card .el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
}

.box-card .el-card__body {
  padding: 20px;
}

.filter-container {
  margin-bottom: 0;
}

.search-form {
  background-color: #f8f9fa;
  padding: 15px;
  margin: 0;
  border-radius: 0;
  font-size: 14px;
  border-bottom: 1px solid #e6e6e6;
}

.search-form .el-form-item__label {
  font-size: 14px !important;
  font-weight: 500;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

.search-form .el-button {
  font-size: 14px;
  padding: 10px 20px;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 操作对象样式 */
.operation-target {
  display: flex;
  align-items: center;
  gap: 6px;
}

.target-name {
  font-size: 12px;
  color: #606266;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
