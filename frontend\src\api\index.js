import axios from 'axios'
import { Message } from 'element-ui'

// 创建axios实例
const request = axios.create({
  baseURL: 'http://localhost:8088/api',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      Message.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    return res
  },
  error => {
    Message.error(error.message || '网络错误')
    return Promise.reject(error)
  }
)

// 软件相关API
export const softwareApi = {
  // 获取软件列表
  getSoftwareList() {
    return request.get('/software/list')
  },

  // 获取软件详情
  getSoftwareDetail(id) {
    return request.get(`/software/${id}`)
  },

  // 记录下载
  recordDownload(id) {
    return request.post(`/software/download/${id}`)
  },

  // 搜索软件
  searchSoftware(keyword) {
    return request.get('/software/search', {
      params: { keyword }
    })
  },

  // 获取分类列表
  getCategoryList() {
    return request.get('/software/categories')
  },

  // 获取热门软件
  getTopSoftware(limit = 10) {
    return request.get('/software/top', { params: { limit } })
  },

  // 获取最新软件
  // 获取最新软件
  getLatestSoftware(limit = 10) {
    return request.get('/software/latest', { params: { limit } })
  },

  // 获取网站配置（前端用）
  getSiteConfig() {
    return request.get('/admin/site-config')
  }
}

// 管理员相关API
export const adminApi = {
  // 管理员登录
  login(data) {
    return request.post('/admin/login', data)
  },

  // 修改密码
  changePassword(data) {
    return request.post('/admin/change-password', data)
  },

  // 更新个人信息
  updateProfile(data) {
    return request.post('/admin/update-profile', data)
  },
  
  // 获取软件列表（管理员，支持分页和搜索）
  getSoftwareList(params) {
    return request.get('/admin/software/list', { params })
  },
  
  // 获取所有软件（管理员）
  getAllSoftware() {
    return request.get('/admin/software/list')
  },
  
  // 添加软件
  addSoftware(data) {
    return request.post('/admin/software', data)
  },
  
  // 更新软件
  updateSoftware(id, data) {
    return request.put(`/admin/software/${id}`, data)
  },
  
  // 删除软件
  deleteSoftware(id) {
    return request.delete(`/admin/software/${id}`)
  },
  
  // 获取软件详情（管理员）
  getSoftwareDetail(id) {
    return request.get(`/admin/software/${id}`)
  },

  // 分类管理相关API
  // 获取分类列表
  getCategoryList() {
    return request.get('/admin/category/list')
  },

  // 添加分类
  addCategory(data) {
    return request.post('/admin/category/add', data)
  },

  // 更新分类
  updateCategory(data) {
    return request.post('/admin/category/update', data)
  },

  // 删除分类
  deleteCategory(id) {
    return request.post(`/admin/category/delete/${id}`)
  },

  // ==================== 网站配置管理 ====================
  // 获取网站配置
  getSiteConfig() {
    return request.get('/admin/site-config')
  },
  
  // 保存网站配置
  saveSiteConfig(config) {
    return request.post('/admin/site-config', config)
  },

  // ==================== 操作记录管理 ====================
  // 获取操作记录列表
  getOperationLogs(params) {
    return request.get('/admin/operation-logs', { params })
  },

  // 获取最近操作记录
  getRecentOperationLogs(params) {
    return request.get('/admin/operation-logs/recent', { params })
  }
}

export default request