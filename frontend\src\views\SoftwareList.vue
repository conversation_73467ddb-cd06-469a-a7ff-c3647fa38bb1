<template>
  <div class="software-list">
    <!-- 网站头部 -->
    <div class="site-header">
      <div class="container">
        <div class="header-top">
          <div class="logo">
            <img v-if="siteConfig.siteLogo" :src="siteConfig.siteLogo" :alt="siteConfig.siteName" />
            <img v-else src="/logo.svg" alt="软件下载网站" />
            <span>{{ siteConfig.siteName || '软件下载之家' }}</span>
          </div>
          <div class="search-area">
            <el-input
              v-model="searchKeyword"
              placeholder="想下载什么软件？"
              @keyup.enter="handleSearch"
              clearable
              class="search-input"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="main-nav">
      <div class="container">
        <ul class="nav-list">
          <li class="nav-item" :class="{ active: activeCategory === null }">
            <a href="#" @click.prevent="setActiveCategory(null)">网站首页</a>
          </li>
          <li
            v-for="category in categoryList"
            :key="category.id"
            class="nav-item dropdown"
            :class="{ active: isParentCategoryActive(category.id) }"
            @mouseenter="showDropdown = category.id"
            @mouseleave="showDropdown = null"
          >
            <a href="#" @click.prevent="setActiveCategory(category.id)" class="dropdown-toggle">
              {{ category.name }}
              <i class="el-icon-arrow-down" v-if="category.children && category.children.length > 0"></i>
            </a>
            <!-- 二级分类下拉菜单 -->
            <ul
              v-if="category.children && category.children.length > 0"
              class="dropdown-menu"
              :class="{ show: showDropdown === category.id }"
            >
              <li v-for="subCategory in category.children" :key="subCategory.id">
                <a
                  href="#"
                  @click.prevent="setActiveCategory(subCategory.id)"
                  :class="{ active: activeCategory === subCategory.id }"
                >
                  {{ subCategory.name }}
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <el-button
              type="text"
              icon="el-icon-setting"
              @click="goToAdmin"
              class="admin-link"
            >
              后台管理
            </el-button>
          </li>
        </ul>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <div class="container">
        <div class="content-wrapper">
          <!-- 左侧分类导航 -->
          <!-- 左侧分类导航 -->
          <div class="sidebar-left">
            <div class="category-nav">
              <div class="nav-header">
                <h3 class="nav-title">全部分类</h3>
                <el-button
                  size="mini"
                  type="text"
                  @click="toggleAllCategories"
                  class="toggle-all-btn"
                >
                  {{ allExpanded ? '全部收起' : '全部展开' }}
                </el-button>
              </div>
              <ul class="category-list">
                <li
                  v-for="category in categoryList"
                  :key="category.id"
                  class="category-item parent-category"
                  :class="{
                    active: isParentCategoryActive(category.id),
                    expanded: expandedCategories.includes(category.id)
                  }"
                >
                  <div class="category-header" @click="toggleCategory(category.id)">
                    <span class="category-name">{{ category.name }}</span>
                    <i
                      v-if="category.children && category.children.length > 0"
                      class="el-icon-arrow-right expand-icon"
                      :class="{ expanded: expandedCategories.includes(category.id) }"
                    ></i>
                  </div>
                  <!-- 二级分类 -->
                  <ul
                    v-if="category.children && category.children.length > 0"
                    class="sub-category-list"
                    :class="{ show: expandedCategories.includes(category.id) }"
                  >
                    <li
                      v-for="subCategory in category.children"
                      :key="subCategory.id"
                      class="sub-category-item"
                      :class="{ active: activeCategory === subCategory.id }"
                      @click.stop="setActiveCategory(subCategory.id)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>

          <!-- 中间内容区 -->
          <div class="content-main">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
              <span>当前位置：</span>
              <a href="#" @click.prevent="setActiveCategory(null)">首页</a>
              <span v-if="activeCategory"> > {{ getCurrentCategoryName() }}</span>
              <span v-if="activeTag"> > {{ activeTag }}</span>
            </div>

            <!-- 软件列表 -->
            <div class="software-grid" v-loading="loading">
              <div
                v-for="software in paginatedSoftwareList"
                :key="software.id"
                class="software-item-wrapper"
              >
                <div class="software-card" @click="goToDetail(software.id)">
                  <div class="software-icon">
                    <img :src="software.icon" :alt="software.name" />
                  </div>
                  <div class="software-info">
                    <h3 class="software-name">{{ software.name }}</h3>
                    <div class="software-version">{{ software.version }}</div>
                    <p class="software-desc">{{ software.description }}</p>
                    <div class="software-tags" v-if="software.tags">
                      <span
                        v-for="tag in parseTags(software.tags)"
                        :key="tag"
                        class="tag-item"
                        @click.stop="filterByTag(tag)"
                      >
                        {{ tag }}
                      </span>
                    </div>
                    <div class="software-meta">
                      <span class="publish-time">发布时间: {{ formatDate(software.publishTime) }}</span>
                    </div>
                    <div class="software-actions">
                      <el-button type="primary" size="small" @click.stop="goToDetailNewWindow(software.id)">
                        立即下载
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && filteredSoftwareList.length === 0" class="empty-state">
              <el-empty description="暂无软件数据"></el-empty>
            </div>

            <!-- 分页 -->
            <div v-if="!loading && filteredSoftwareList.length > 0" class="pagination-wrapper">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredSoftwareList.length">
              </el-pagination>
            </div>
          </div>

          <!-- 右侧边栏 -->
          <div class="sidebar-right">
            <!-- 本周排行 -->
            <div class="widget">
              <h3 class="widget-title">本周排行</h3>
              <ul class="ranking-list">
                <li
                  v-for="(software, index) in topSoftwareList"
                  :key="software.id"
                  class="ranking-item"
                  @click="goToDetail(software.id)"
                >
                  <span class="rank" :class="'rank-' + (index + 1)">{{ index + 1 }}</span>
                  <span class="name">{{ software.name }}</span>
                  <span class="version">{{ software.version }}</span>
                </li>
              </ul>
            </div>

            <!-- 最新软件 -->
            <div class="widget">
              <h3 class="widget-title">最新软件</h3>
              <ul class="latest-list">
                <li
                  v-for="software in latestSoftwareList"
                  :key="software.id"
                  class="latest-item"
                  @click="goToDetail(software.id)"
                >
                  <div class="latest-icon">
                    <img :src="software.icon" :alt="software.name" />
                  </div>
                  <div class="latest-info">
                    <div class="name">{{ software.name }}</div>
                    <div class="date">{{ formatDate(software.createdAt) }}</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网站底部 -->
    <div class="site-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <p v-if="siteConfig.siteDescription" class="site-desc">{{ siteConfig.siteDescription }}</p>
            <p v-if="siteConfig.siteKeywords" class="site-keywords">
              <strong>关键词：</strong>{{ siteConfig.siteKeywords }}
            </p>
            <div class="footer-links">
              <span v-if="siteConfig.contactEmail" class="contact-email">
                <i class="el-icon-message"></i>
                联系邮箱：{{ siteConfig.contactEmail }}
              </span>
              <span v-if="siteConfig.icp" class="icp-info">
                <i class="el-icon-document"></i>
                {{ siteConfig.icp }}
              </span>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 {{ siteConfig.siteName || '软件下载之家' }}. All rights reserved.</p>
        </div>
      </div>
    </div>

    <!-- 全屏图片预览 -->
    <div v-if="previewVisible" class="fullscreen-image-preview" @click.self="closePreview">
      <div class="preview-image-container" @click.self="closePreview">
        <img
          :src="previewImageUrl"
          class="fullscreen-preview-image"
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
          @mousedown="startDrag"
          @mousemove="onDrag"
          @mouseup="endDrag"
          @mouseleave="endDrag"
        />
      </div>

      <!-- 底部控制栏 -->
      <div class="preview-controls-bottom">
        <div class="controls-wrapper">
          <el-button @click="zoomOut" icon="el-icon-zoom-out" circle size="medium"></el-button>
          <el-button @click="zoomIn" icon="el-icon-zoom-in" circle size="medium"></el-button>
          <el-button @click="resetZoom" icon="el-icon-refresh" circle size="medium"></el-button>
          <el-button @click="rotateLeft" icon="el-icon-refresh-left" circle size="medium"></el-button>
          <el-button @click="rotateRight" icon="el-icon-refresh-right" circle size="medium"></el-button>
          <el-button @click="closePreview" icon="el-icon-close" circle size="medium"></el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { softwareApi } from '@/api'
import { parseTags } from '@/utils'

export default {
  name: 'SoftwareList',
  data() {
    return {
      softwareList: [],
      categoryList: [],
      topSoftwareList: [],
      latestSoftwareList: [],
      loading: false,
      searchKeyword: '',
      activeCategory: null,
      activeTag: null,
      showDropdown: null, // 控制下拉菜单显示
      expandedCategories: [], // 展开的分类ID列表
      siteConfig: {}, // 网站配置
      // 图片预览相关
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      // 分页相关
      currentPage: 1,
      pageSize: 18
    }
  },
  computed: {
    // 过滤后的软件列表
    filteredSoftwareList() {
      let list = this.softwareList

      // 按分类过滤
      if (this.activeCategory) {
        list = list.filter(software => {
          // 如果软件直接属于当前分类
          if (software.categoryId === this.activeCategory) {
            return true
          }

          // 检查是否属于当前分类的子分类
          const parentCategory = this.categoryList.find(cat => cat.id === this.activeCategory)
          if (parentCategory && parentCategory.children) {
            return parentCategory.children.some(child => child.id === software.categoryId)
          }

          return false
        })
      }

      // 按标签过滤
      if (this.activeTag) {
        list = list.filter(software => {
          const tags = this.parseTags(software.tags)
          return tags.includes(this.activeTag)
        })
      }

      // 按搜索关键词过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(software =>
          software.name.toLowerCase().includes(keyword) ||
          software.description.toLowerCase().includes(keyword)
        )
      }

      return list
    },

    // 判断是否全部展开
    allExpanded() {
      const categoriesWithChildren = this.categoryList.filter(category =>
        category.children && category.children.length > 0
      )
      return categoriesWithChildren.length > 0 &&
             categoriesWithChildren.every(category =>
               this.expandedCategories.includes(category.id)
             )
    },

    // 分页后的软件列表
    paginatedSoftwareList() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredSoftwareList.slice(start, end)
    },

    // 图片样式
    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    },

    // 对话框宽度（根据屏幕大小自适应）
    dialogWidth() {
      const screenWidth = window.innerWidth
      if (screenWidth <= 768) {
        return '95%'
      } else if (screenWidth <= 1024) {
        return '90%'
      } else {
        return '85%'
      }
    },

    // 对话框顶部距离
    dialogTop() {
      return '3vh'
    }
  },
  created() {
    this.loadSoftwareList()
    this.loadCategoryList()
    this.loadTopSoftware()
    this.loadLatestSoftware()
    this.loadSiteConfig()
    this.handleRouteQuery()
  },
  methods: {
    parseTags,

    // 跳转到后台管理
    goToAdmin() {
      this.$router.push('/admin/login')
    },

    // 设置活动分类
    setActiveCategory(categoryId) {
      this.activeCategory = categoryId
      this.activeTag = null // 重置标签过滤
      this.currentPage = 1 // 重置到第一页
    },

    // 设置活动标签
    setActiveTag(tag) {
      this.activeTag = this.activeTag === tag ? null : tag
    },

    // 判断父分类是否处于活动状态
    isParentCategoryActive(parentId) {
      if (this.activeCategory === parentId) return true

      // 检查是否有子分类处于活动状态
      const parentCategory = this.categoryList.find(c => c.id === parentId)
      if (parentCategory && parentCategory.children) {
        return parentCategory.children.some(child => child.id === this.activeCategory)
      }

      return false
    },

    // 获取当前分类名称
    getCurrentCategoryName() {
      if (!this.activeCategory) return '全部分类'

      // 先在一级分类中查找
      let category = this.categoryList.find(c => c.id === this.activeCategory)
      if (category) return category.name

      // 在二级分类中查找
      for (const parent of this.categoryList) {
        if (parent.children) {
          category = parent.children.find(c => c.id === this.activeCategory)
          if (category) return category.name
        }
      }

      return '全部分类'
    },

    // 获取当前分类的标签
    getCurrentCategoryTags() {
      if (!this.activeCategory) {
        // 获取所有标签
        const allTags = new Set()
        this.softwareList.forEach(software => {
          const tags = this.parseTags(software.tags)
          tags.forEach(tag => allTags.add(tag))
        })
        return Array.from(allTags)
      } else {
        // 获取当前分类的标签（包含子分类）
        const categoryTags = new Set()
        this.softwareList
          .filter(software => {
            // 直接属于当前分类
            if (software.categoryId === this.activeCategory) {
              return true
            }

            // 属于当前分类的子分类
            const parentCategory = this.categoryList.find(cat => cat.id === this.activeCategory)
            if (parentCategory && parentCategory.children) {
              return parentCategory.children.some(child => child.id === software.categoryId)
            }

            return false
          })
          .forEach(software => {
            const tags = this.parseTags(software.tags)
            tags.forEach(tag => categoryTags.add(tag))
          })
        return Array.from(categoryTags)
      }
    },

    // 获取分类软件数量
    getCategoryCount(categoryId) {
      let count = 0

      // 计算直接属于该分类的软件数量
      count += this.softwareList.filter(software => software.categoryId === categoryId).length

      // 计算属于该分类子分类的软件数量
      const category = this.categoryList.find(cat => cat.id === categoryId)
      if (category && category.children) {
        category.children.forEach(child => {
          count += this.softwareList.filter(software => software.categoryId === child.id).length
        })
      }

      return count
    },

    // 获取分类名称
    getCategoryName(categoryId) {
      // 先在一级分类中查找
      let category = this.categoryList.find(c => c.id === categoryId)
      if (category) return category.name

      // 在二级分类中查找
      for (const parent of this.categoryList) {
        if (parent.children) {
          category = parent.children.find(c => c.id === categoryId)
          if (category) return category.name
        }
      }

      return '未分类'
    },

    // 获取一级分类下的软件总数（包括其所有子分类）
    getParentCategoryCount(parentCategory) {
      let count = 0

      // 统计直接属于该一级分类的软件
      count += this.softwareList.filter(software => software.categoryId === parentCategory.id).length

      // 统计属于该一级分类下所有子分类的软件
      if (parentCategory.children && parentCategory.children.length > 0) {
        parentCategory.children.forEach(childCategory => {
          count += this.softwareList.filter(software => software.categoryId === childCategory.id).length
        })
      }

      return count
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    async loadSoftwareList() {
      this.loading = true
      try {
        const res = await softwareApi.getSoftwareList()
        // 处理图标URL，确保指向正确的服务器
        this.softwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载软件列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载分类列表
    async loadCategoryList() {
      try {
        const res = await softwareApi.getCategoryList()
        this.categoryList = res.data || []
        // 默认全部收缩
        this.expandedCategories = []
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    },

    // 切换分类展开/收起状态
    toggleCategory(categoryId) {
      const index = this.expandedCategories.indexOf(categoryId)
      if (index > -1) {
        this.expandedCategories.splice(index, 1)
      } else {
        this.expandedCategories.push(categoryId)
      }
    },

    // 一键展开/收起所有分类
    toggleAllCategories() {
      if (this.allExpanded) {
        // 全部收起
        this.expandedCategories = []
      } else {
        // 全部展开
        this.expandedCategories = this.categoryList
          .filter(category => category.children && category.children.length > 0)
          .map(category => category.id)
      }
    },


    // 加载热门软件
    async loadTopSoftware() {
      try {
        const res = await softwareApi.getTopSoftware(10)
        this.topSoftwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载热门软件失败:', error)
      }
    },

    // 加载最新软件
    async loadLatestSoftware() {
      try {
        const res = await softwareApi.getLatestSoftware(8)
        this.latestSoftwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载最新软件失败:', error)
      }
    },

    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.loadSoftwareList()
        return
      }

      this.loading = true
      try {
        const res = await softwareApi.searchSoftware(this.searchKeyword)
        // 处理搜索结果中的图标URL
        this.softwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('搜索失败:', error)
        // API失败时使用前端筛选搜索
        this.performFrontendSearch()
      } finally {
        this.loading = false
      }
    },

    // 前端搜索功能
    performFrontendSearch() {
      if (!this.searchKeyword.trim()) {
        return
      }

      const keyword = this.searchKeyword.toLowerCase()

      // 从原始软件列表中搜索（确保有完整数据）
      this.loadSoftwareList().then(() => {
        this.softwareList = this.softwareList.filter(software => {
          // 搜索软件名称
          const nameMatch = software.name && software.name.toLowerCase().includes(keyword)

          // 搜索软件描述
          const descMatch = software.description && software.description.toLowerCase().includes(keyword)

          // 搜索详细描述
          const detailDescMatch = software.detailDescription && software.detailDescription.toLowerCase().includes(keyword)

          // 搜索发布商
          const publisherMatch = software.publisher && software.publisher.toLowerCase().includes(keyword)

          // 搜索标签
          const tagsMatch = software.tags && software.tags.toLowerCase().includes(keyword)

          return nameMatch || descMatch || detailDescMatch || publisherMatch || tagsMatch
        })

        // 重置筛选条件，显示搜索结果
        this.activeCategory = null
        this.activeTag = null
        this.currentPage = 1
      })
    },

    async handleDownload(software) {
      try {
        await softwareApi.recordDownload(software.id)
        software.downloadCount++
        window.open(software.downloadUrl, '_blank')
        this.$message.success('开始下载')
      } catch (error) {
        console.error('下载失败:', error)
      }
    },

    goToDetail(id) {
      this.$router.push(`/software/${id}`)
    },

    // 新窗口打开详情页
    goToDetailNewWindow(id) {
      const url = this.$router.resolve(`/software/${id}`).href
      window.open(url, '_blank')
    },

    // 解析标签字符串
    parseTags(tags) {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    },

    // 根据标签筛选
    filterByTag(tag) {
      this.activeTag = tag
      this.activeCategory = null
      this.currentPage = 1 // 重置到第一页
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 加载网站配置
    async loadSiteConfig() {
      try {
        const res = await softwareApi.getSiteConfig()
        this.siteConfig = res.data || {}
        // 处理LOGO URL
        if (this.siteConfig.siteLogo && !this.siteConfig.siteLogo.startsWith('http')) {
          this.siteConfig.siteLogo = `http://localhost:8088${this.siteConfig.siteLogo}`
        }
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    },

    // 预览图片
    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
      // 重置图片状态
      this.resetImageState()
    },

    // 重置图片状态
    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    // 放大图片
    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    // 缩小图片
    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    // 重置缩放
    resetZoom() {
      this.resetImageState()
    },

    // 左旋转
    rotateLeft() {
      this.imageRotation -= 90
    },

    // 右旋转
    rotateRight() {
      this.imageRotation += 90
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    // 拖拽中
    onDrag(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
      }
    },

    // 结束拖拽
    endDrag() {
      this.isDragging = false
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
    },

    // 处理路由查询参数
    handleRouteQuery() {
      const query = this.$route.query

      // 处理分类查询参数
      if (query.category) {
        const categoryId = parseInt(query.category)
        this.setActiveCategory(categoryId)
      }

      // 处理搜索查询参数
      if (query.search) {
        this.searchKeyword = query.search
      }
    }
  }
}
</script>

<style src="../styles/SoftwareList.css" scoped>
</style>
