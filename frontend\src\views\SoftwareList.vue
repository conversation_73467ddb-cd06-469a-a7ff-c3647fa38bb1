<template>
  <div class="software-list">
    <!-- 网站头部 -->
    <div class="site-header">
      <div class="container">
        <div class="header-top">
          <div class="logo">
            <img v-if="siteConfig.siteLogo" :src="siteConfig.siteLogo" :alt="siteConfig.siteName" />
            <img v-else src="/logo.png" alt="软件下载网站" />
            <span>{{ siteConfig.siteName || '软件下载之家' }}</span>
          </div>
          <div class="search-area">
            <el-input
              v-model="searchKeyword"
              placeholder="想下载什么软件？"
              @keyup.enter="handleSearch"
              clearable
              class="search-input"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="main-nav">
      <div class="container">
        <ul class="nav-list">
          <li class="nav-item" :class="{ active: activeCategory === null }">
            <a href="#" @click.prevent="setActiveCategory(null)">网站首页</a>
          </li>
          <li
            v-for="category in categoryList"
            :key="category.id"
            class="nav-item dropdown"
            :class="{ active: isParentCategoryActive(category.id) }"
            @mouseenter="showDropdown = category.id"
            @mouseleave="showDropdown = null"
          >
            <a href="#" @click.prevent="setActiveCategory(category.id)" class="dropdown-toggle">
              {{ category.name }}
              <i class="el-icon-arrow-down" v-if="category.children && category.children.length > 0"></i>
            </a>
            <!-- 二级分类下拉菜单 -->
            <ul
              v-if="category.children && category.children.length > 0"
              class="dropdown-menu"
              :class="{ show: showDropdown === category.id }"
            >
              <li v-for="subCategory in category.children" :key="subCategory.id">
                <a
                  href="#"
                  @click.prevent="setActiveCategory(subCategory.id)"
                  :class="{ active: activeCategory === subCategory.id }"
                >
                  {{ subCategory.name }}
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <el-button
              type="text"
              icon="el-icon-setting"
              @click="goToAdmin"
              class="admin-link"
            >
              后台管理
            </el-button>
          </li>
        </ul>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <div class="container">
        <div class="content-wrapper">
          <!-- 左侧分类导航 -->
          <!-- 左侧分类导航 -->
          <div class="sidebar-left">
            <div class="category-nav">
              <div class="nav-header">
                <h3 class="nav-title">全部分类</h3>
                <el-button
                  size="mini"
                  type="text"
                  @click="toggleAllCategories"
                  class="toggle-all-btn"
                >
                  {{ allExpanded ? '全部收起' : '全部展开' }}
                </el-button>
              </div>
              <ul class="category-list">
                <li
                  v-for="category in categoryList"
                  :key="category.id"
                  class="category-item parent-category"
                  :class="{ 
                    active: isParentCategoryActive(category.id),
                    expanded: expandedCategories.includes(category.id)
                  }"
                >
                  <div class="category-header" @click="toggleCategory(category.id)">
                    <span class="category-name">{{ category.name }}</span>
                    <i 
                      v-if="category.children && category.children.length > 0"
                      class="el-icon-arrow-right expand-icon"
                      :class="{ expanded: expandedCategories.includes(category.id) }"
                    ></i>
                  </div>
                  <!-- 二级分类 -->
                  <ul 
                    v-if="category.children && category.children.length > 0"
                    class="sub-category-list"
                    :class="{ show: expandedCategories.includes(category.id) }"
                  >
                    <li
                      v-for="subCategory in category.children"
                      :key="subCategory.id"
                      class="sub-category-item"
                      :class="{ active: activeCategory === subCategory.id }"
                      @click.stop="setActiveCategory(subCategory.id)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>

          <!-- 中间内容区 -->
          <div class="content-main">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
              <span>当前位置：</span>
              <a href="#" @click.prevent="setActiveCategory(null)">首页</a>
              <span v-if="activeCategory"> > {{ getCurrentCategoryName() }}</span>
              <span v-if="activeTag"> > {{ activeTag }}</span>
            </div>

            <!-- 软件列表 -->
            <div class="software-grid" v-loading="loading">
              <div
                v-for="software in paginatedSoftwareList"
                :key="software.id"
                class="software-card"
                @click="goToDetail(software.id)"
              >
                <div class="software-icon">
                  <img :src="software.icon" :alt="software.name" />
                </div>
                <div class="software-info">
                  <h3 class="software-name">{{ software.name }}</h3>
                  <p class="software-version">{{ software.version }}</p>
                  <p class="software-desc">{{ software.description }}</p>

                  <!-- 软件标签 -->
                  <div v-if="software.tags" class="software-tags">
                    <el-tag
                      v-for="tag in parseTags(software.tags)"
                      :key="tag"
                      size="mini"
                      class="tag-item"
                      @click.stop="filterByTag(tag)">
                      {{ tag }}
                    </el-tag>
                  </div>

                  <div class="software-meta">
                    <span class="publish-time">发布时间: {{ formatDate(software.publishTime) }}</span>
                  </div>
                </div>
                <div class="software-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="goToDetailNewWindow(software.id)"
                  >
                    立即下载
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && filteredSoftwareList.length === 0" class="empty-state">
              <el-empty description="暂无软件数据"></el-empty>
            </div>

            <!-- 分页 -->
            <div v-if="!loading && filteredSoftwareList.length > 0" class="pagination-wrapper">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredSoftwareList.length">
              </el-pagination>
            </div>
          </div>

          <!-- 右侧边栏 -->
          <div class="sidebar-right">
            <!-- 栏目分类 -->
            <div class="widget">
              <h3 class="widget-title">栏目分类</h3>
              <ul class="widget-list">
                <li v-for="category in categoryList" :key="category.id">
                  <a href="#" @click.prevent="setActiveCategory(category.id)">
                    {{ category.name }}
                  </a>
                  <span class="count">({{ getCategoryCount(category.id) }})</span>
                </li>
              </ul>
            </div>

            <!-- 本周排行 -->
            <div class="widget">
              <h3 class="widget-title">本周排行</h3>
              <ul class="ranking-list">
                <li
                  v-for="(software, index) in topSoftwareList"
                  :key="software.id"
                  class="ranking-item"
                  @click="goToDetail(software.id)"
                >
                  <span class="rank" :class="'rank-' + (index + 1)">{{ index + 1 }}</span>
                  <span class="name">{{ software.name }}</span>
                  <span class="version">{{ software.version }}</span>
                </li>
              </ul>
            </div>

            <!-- 最新软件 -->
            <div class="widget">
              <h3 class="widget-title">最新软件</h3>
              <ul class="latest-list">
                <li
                  v-for="software in latestSoftwareList"
                  :key="software.id"
                  class="latest-item"
                  @click="goToDetail(software.id)"
                >
                  <div class="latest-icon">
                    <img :src="software.icon" :alt="software.name" />
                  </div>
                  <div class="latest-info">
                    <div class="name">{{ software.name }}</div>
                    <div class="date">{{ formatDate(software.createdAt) }}</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网站底部 -->
    <div class="site-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <p v-if="siteConfig.siteDescription" class="site-desc">{{ siteConfig.siteDescription }}</p>
            <p v-if="siteConfig.siteKeywords" class="site-keywords">
              <strong>关键词：</strong>{{ siteConfig.siteKeywords }}
            </p>
            <div class="footer-links">
              <span v-if="siteConfig.contactEmail" class="contact-email">
                <i class="el-icon-message"></i>
                联系邮箱：{{ siteConfig.contactEmail }}
              </span>
              <span v-if="siteConfig.icp" class="icp-info">
                <i class="el-icon-document"></i>
                {{ siteConfig.icp }}
              </span>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 {{ siteConfig.siteName || '软件下载之家' }}. All rights reserved.</p>
        </div>
      </div>
    </div>

    <!-- 全屏图片预览 -->
    <div v-if="previewVisible" class="fullscreen-image-preview" @click.self="closePreview">
      <div class="preview-image-container" @click.self="closePreview">
        <img 
          :src="previewImageUrl" 
          class="fullscreen-preview-image" 
          :style="imageStyle"
          @dragstart.prevent
          @click.stop
          @mousedown="startDrag" 
          @mousemove="onDrag" 
          @mouseup="endDrag" 
          @mouseleave="endDrag"
        />
      </div>
      
      <!-- 底部控制栏 -->
      <div class="preview-controls-bottom">
        <div class="controls-wrapper">
          <el-button @click="zoomOut" icon="el-icon-zoom-out" circle size="medium"></el-button>
          <el-button @click="zoomIn" icon="el-icon-zoom-in" circle size="medium"></el-button>
          <el-button @click="resetZoom" icon="el-icon-refresh" circle size="medium"></el-button>
          <el-button @click="rotateLeft" icon="el-icon-refresh-left" circle size="medium"></el-button>
          <el-button @click="rotateRight" icon="el-icon-refresh-right" circle size="medium"></el-button>
          <el-button @click="closePreview" icon="el-icon-close" circle size="medium"></el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { softwareApi } from '@/api'
import { parseTags } from '@/utils'

export default {
  name: 'SoftwareList',
  data() {
    return {
      softwareList: [],
      categoryList: [],
      topSoftwareList: [],
      latestSoftwareList: [],
      loading: false,
      searchKeyword: '',
      activeCategory: null,
      activeTag: null,
      showDropdown: null, // 控制下拉菜单显示
      expandedCategories: [], // 展开的分类ID列表
      siteConfig: {}, // 网站配置
      // 图片预览相关
      previewVisible: false,
      previewImageUrl: '',
      imageScale: 1,
      imageRotation: 0,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      // 分页相关
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    // 过滤后的软件列表
    filteredSoftwareList() {
      let list = this.softwareList

      // 按分类过滤
      if (this.activeCategory) {
        list = list.filter(software => software.categoryId === this.activeCategory)
      }

      // 按标签过滤
      if (this.activeTag) {
        list = list.filter(software => {
          const tags = this.parseTags(software.tags)
          return tags.includes(this.activeTag)
        })
      }

      // 按搜索关键词过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(software =>
          software.name.toLowerCase().includes(keyword) ||
          software.description.toLowerCase().includes(keyword)
        )
      }

      return list
    },

    // 判断是否全部展开
    allExpanded() {
      const categoriesWithChildren = this.categoryList.filter(category =>
        category.children && category.children.length > 0
      )
      return categoriesWithChildren.length > 0 &&
             categoriesWithChildren.every(category =>
               this.expandedCategories.includes(category.id)
             )
    },

    // 分页后的软件列表
    paginatedSoftwareList() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredSoftwareList.slice(start, end)
    },

    // 图片样式
    imageStyle() {
      return {
        transform: `scale(${this.imageScale}) rotate(${this.imageRotation}deg) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`,
        cursor: this.isDragging ? 'grabbing' : 'grab',
        transition: this.isDragging ? 'none' : 'transform 0.3s ease'
      }
    },

    // 对话框宽度（根据屏幕大小自适应）
    dialogWidth() {
      const screenWidth = window.innerWidth
      if (screenWidth <= 768) {
        return '95%'
      } else if (screenWidth <= 1024) {
        return '90%'
      } else {
        return '85%'
      }
    },

    // 对话框顶部距离
    dialogTop() {
      return '3vh'
    }
  },
  created() {
    this.loadSoftwareList()
    this.loadCategoryList()
    this.loadTopSoftware()
    this.loadLatestSoftware()
    this.loadSiteConfig()
  },
  methods: {
    parseTags,

    // 跳转到后台管理
    goToAdmin() {
      this.$router.push('/admin/login')
    },

    // 设置活动分类
    setActiveCategory(categoryId) {
      this.activeCategory = categoryId
      this.activeTag = null // 重置标签过滤
      this.currentPage = 1 // 重置到第一页
    },

    // 设置活动标签
    setActiveTag(tag) {
      this.activeTag = this.activeTag === tag ? null : tag
    },

    // 判断父分类是否处于活动状态
    isParentCategoryActive(parentId) {
      if (this.activeCategory === parentId) return true

      // 检查是否有子分类处于活动状态
      const parentCategory = this.categoryList.find(c => c.id === parentId)
      if (parentCategory && parentCategory.children) {
        return parentCategory.children.some(child => child.id === this.activeCategory)
      }

      return false
    },

    // 获取当前分类名称
    getCurrentCategoryName() {
      if (!this.activeCategory) return '全部分类'

      // 先在一级分类中查找
      let category = this.categoryList.find(c => c.id === this.activeCategory)
      if (category) return category.name

      // 在二级分类中查找
      for (const parent of this.categoryList) {
        if (parent.children) {
          category = parent.children.find(c => c.id === this.activeCategory)
          if (category) return category.name
        }
      }

      return '全部分类'
    },

    // 获取当前分类的标签
    getCurrentCategoryTags() {
      if (!this.activeCategory) {
        // 获取所有标签
        const allTags = new Set()
        this.softwareList.forEach(software => {
          const tags = this.parseTags(software.tags)
          tags.forEach(tag => allTags.add(tag))
        })
        return Array.from(allTags)
      } else {
        // 获取当前分类的标签
        const categoryTags = new Set()
        this.softwareList
          .filter(software => software.categoryId === this.activeCategory)
          .forEach(software => {
            const tags = this.parseTags(software.tags)
            tags.forEach(tag => categoryTags.add(tag))
          })
        return Array.from(categoryTags)
      }
    },

    // 获取分类软件数量
    getCategoryCount(categoryId) {
      return this.softwareList.filter(software => software.categoryId === categoryId).length
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    async loadSoftwareList() {
      this.loading = true
      try {
        const res = await softwareApi.getSoftwareList()
        // 处理图标URL，确保指向正确的服务器
        this.softwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载软件列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载分类列表
    async loadCategoryList() {
      try {
        const res = await softwareApi.getCategoryList()
        this.categoryList = res.data || []
        // 默认全部收缩
        this.expandedCategories = []
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    },

    // 切换分类展开/收起状态
    toggleCategory(categoryId) {
      const index = this.expandedCategories.indexOf(categoryId)
      if (index > -1) {
        this.expandedCategories.splice(index, 1)
      } else {
        this.expandedCategories.push(categoryId)
      }
    },

    // 一键展开/收起所有分类
    toggleAllCategories() {
      if (this.allExpanded) {
        // 全部收起
        this.expandedCategories = []
      } else {
        // 全部展开
        this.expandedCategories = this.categoryList
          .filter(category => category.children && category.children.length > 0)
          .map(category => category.id)
      }
    },

    // 加载热门软件
    async loadTopSoftware() {
      try {
        const res = await softwareApi.getTopSoftware(10)
        this.topSoftwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载热门软件失败:', error)
      }
    },

    // 加载最新软件
    async loadLatestSoftware() {
      try {
        const res = await softwareApi.getLatestSoftware(8)
        this.latestSoftwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('加载最新软件失败:', error)
      }
    },

    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.loadSoftwareList()
        return
      }
      
      this.loading = true
      try {
        const res = await softwareApi.searchSoftware(this.searchKeyword)
        // 处理搜索结果中的图标URL
        this.softwareList = (res.data || []).map(software => ({
          ...software,
          icon: software.icon && !software.icon.startsWith('http')
            ? `http://localhost:8088${software.icon}`
            : software.icon
        }))
      } catch (error) {
        console.error('搜索失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async handleDownload(software) {
      try {
        await softwareApi.recordDownload(software.id)
        software.downloadCount++
        window.open(software.downloadUrl, '_blank')
        this.$message.success('开始下载')
      } catch (error) {
        console.error('下载失败:', error)
      }
    },
    
    goToDetail(id) {
      this.$router.push(`/software/${id}`)
    },

    // 新窗口打开详情页
    goToDetailNewWindow(id) {
      const url = this.$router.resolve(`/software/${id}`).href
      window.open(url, '_blank')
    },

    // 解析标签字符串
    parseTags(tags) {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    },

    // 根据标签筛选
    filterByTag(tag) {
      this.activeTag = tag
      this.activeCategory = null
      this.currentPage = 1 // 重置到第一页
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 加载网站配置
    async loadSiteConfig() {
      try {
        const res = await softwareApi.getSiteConfig()
        this.siteConfig = res.data || {}
        // 处理LOGO URL
        if (this.siteConfig.siteLogo && !this.siteConfig.siteLogo.startsWith('http')) {
          this.siteConfig.siteLogo = `http://localhost:8088${this.siteConfig.siteLogo}`
        }
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    },

    // 预览图片
    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
      // 重置图片状态
      this.resetImageState()
    },

    // 重置图片状态
    resetImageState() {
      this.imageScale = 1
      this.imageRotation = 0
      this.imagePosition = { x: 0, y: 0 }
      this.isDragging = false
    },

    // 放大图片
    zoomIn() {
      this.imageScale = Math.min(this.imageScale * 1.2, 5)
    },

    // 缩小图片
    zoomOut() {
      this.imageScale = Math.max(this.imageScale / 1.2, 0.1)
    },

    // 重置缩放
    resetZoom() {
      this.resetImageState()
    },

    // 左旋转
    rotateLeft() {
      this.imageRotation -= 90
    },

    // 右旋转
    rotateRight() {
      this.imageRotation += 90
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true
      this.dragStart = {
        x: event.clientX - this.imagePosition.x,
        y: event.clientY - this.imagePosition.y
      }
    },

    // 拖拽中
    onDrag(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
      }
    },

    // 结束拖拽
    endDrag() {
      this.isDragging = false
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
    }
  }
}
</script>

<style scoped>
.software-list {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 网站头部 */
.site-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 15px 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.logo img {
  width: 40px;
  height: 40px;
}

.search-area {
  flex: 1;
  max-width: 500px;
  margin: 0 50px;
}

.search-input {
  width: 100%;
}

/* 主导航 */
.main-nav {
  background: #4a90e2;
  border-bottom: 1px solid #357abd;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav-item:hover a,
.nav-item.active a {
  background: rgba(255, 255, 255, 0.1);
}

.admin-link {
  color: #fff !important;
  padding: 15px 20px !important;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-toggle i {
  font-size: 12px;
  transition: transform 0.3s;
}

.dropdown:hover .dropdown-toggle i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 0 0 5px 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  list-style: none;
  margin: 0;
  padding: 0;
  min-width: 150px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child {
  border-bottom: none;
}

.dropdown-menu a {
  display: block;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  background: #f8f9fa;
  color: #4a90e2;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 主体内容 */
.main-content {
  padding: 20px 0;
}

.content-wrapper {
  display: flex;
  gap: 20px;
}

/* 左侧分类导航 */
.sidebar-left {
  width: 200px;
  flex-shrink: 0;
}

.category-nav {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.nav-header {
  background: #4a90e2;
  color: #fff;
  padding: 12px 15px;
  border-radius: 5px 5px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.toggle-all-btn {
  font-size: 12px;
  color: #fff;
  padding: 0;
  opacity: 0.8;
}

.toggle-all-btn:hover {
  opacity: 1;
  color: #fff;
}

.category-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.category-item {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.category-item:last-child {
  border-bottom: none;
}

/* 一级分类样式 */
.parent-category .category-header {
  padding: 12px 15px;
  cursor: pointer;
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.parent-category .category-header:hover {
  background: #e9ecef;
}

.parent-category.active .category-header {
  background: #4a90e2;
  color: white;
}

.expand-icon {
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 二级分类样式 */
.sub-category-list {
  list-style: none;
  margin: 0;
  padding: 0;
  background: #fff;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.sub-category-list.show {
  max-height: 500px;
}

.sub-category-item {
  padding: 8px 30px;
  cursor: pointer;
  color: #666;
  font-size: 13px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s;
}

.sub-category-item:hover {
  background: #f8f9fa;
  color: #4a90e2;
  padding-left: 35px;
}

.sub-category-item.active {
  background: #e3f2fd;
  color: #4a90e2;
  font-weight: 500;
  border-left: 3px solid #4a90e2;
}

.sub-category-item:last-child {
  border-bottom: none;
}


/* 中间内容区 */
.content-main {
  flex: 1;
}

.breadcrumb {
  background: #fff;
  padding: 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 12px;
  color: #666;
}

.breadcrumb a {
  color: #4a90e2;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.software-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 15px;
}

.software-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.software-card:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
}

.software-icon {
  float: left;
  margin-right: 15px;
  margin-bottom: 10px;
}

.software-icon img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  object-fit: cover;
}

.software-info {
  overflow: hidden;
}

.software-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 5px 0;
  line-height: 1.2;
}

.software-version {
  font-size: 12px;
  color: #4a90e2;
  margin: 0 0 8px 0;
}

.software-desc {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 软件标签样式 */
.software-tags {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.software-tags .tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f0f2f5;
  border-color: #d9d9d9;
  color: #666;
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
}

.software-tags .tag-item:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  transform: translateY(-1px);
}

.software-meta {
  font-size: 11px;
  color: #999;
  margin-bottom: 10px;
}

.publish-time {
  color: #999;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding: 20px 0;
}

.pagination-wrapper .el-pagination {
  text-align: center;
}

.software-actions {
  text-align: right;
  clear: both;
}

/* 软件截图样式 */

/* 右侧边栏 */
.sidebar-right {
  width: 250px;
  flex-shrink: 0;
}

.widget {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  margin-bottom: 20px;
}

.widget-title {
  background: #4a90e2;
  color: #fff;
  margin: 0;
  padding: 12px 15px;
  font-size: 14px;
  border-radius: 5px 5px 0 0;
}

.widget-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.widget-list li {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.widget-list li:last-child {
  border-bottom: none;
}

.widget-list a {
  color: #333;
  text-decoration: none;
  font-size: 13px;
}

.widget-list a:hover {
  color: #4a90e2;
}

.count {
  color: #999;
  font-size: 12px;
}

/* 排行榜 */
.ranking-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.ranking-item {
  padding: 8px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.ranking-item:hover {
  background: #f8f9fa;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  background: #ccc;
}

.rank-1 { background: #ff6b6b; }
.rank-2 { background: #4ecdc4; }
.rank-3 { background: #45b7d1; }

.ranking-item .name {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.ranking-item .version {
  font-size: 11px;
  color: #999;
}

/* 最新软件 */
.latest-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.latest-item {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.latest-item:hover {
  background: #f8f9fa;
}

.latest-item:last-child {
  border-bottom: none;
}

.latest-icon img {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

/* 分类导航动画效果 */
.parent-category {
  transition: all 0.3s ease;
}

.parent-category:hover {
  background: #f8f9fa;
}

.category-header {
  transition: all 0.2s ease;
}

.sub-category-list {
  transition: max-height 0.4s ease, opacity 0.3s ease;
  opacity: 0;
}

.sub-category-list.show {
  opacity: 1;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .nav-header {
    padding: 10px 12px;
  }
  
  .nav-title {
    font-size: 13px;
  }
  
  .toggle-all-btn {
    font-size: 11px;
  }
}

.latest-info {
  flex: 1;
}

.latest-info .name {
  font-size: 13px;
  color: #333;
  margin-bottom: 2px;
}

.latest-info .date {
  font-size: 11px;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

/* 网站底部样式 */
.site-footer {
  background: #2c3e50;
  color: #ecf0f1;
  margin-top: 40px;
  padding: 30px 0 20px;
}

.footer-content {
  margin-bottom: 20px;
}

.footer-info {
  text-align: center;
}

.site-desc {
  font-size: 16px;
  margin-bottom: 10px;
  color: #bdc3c7;
}

.site-keywords {
  font-size: 14px;
  margin-bottom: 15px;
  color: #95a5a6;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.contact-email,
.icp-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #bdc3c7;
}

.contact-email i,
.icp-info i {
  color: #3498db;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 15px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  font-size: 13px;
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 15px;
  }

  .search-area {
    margin: 0;
    max-width: 100%;
  }

  .nav-list {
    flex-wrap: wrap;
  }

  .nav-item a {
    padding: 12px 15px;
    font-size: 14px;
  }

  .content-wrapper {
    flex-direction: column;
  }

  .sidebar-left,
  .sidebar-right {
    width: 100%;
  }

  .software-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 中等屏幕 */
@media (max-width: 1200px) {
  .software-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 小屏幕 */
@media (max-width: 900px) {
  .software-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .logo {
    font-size: 20px;
  }

  .logo img {
    width: 32px;
    height: 32px;
  }

  .nav-item a {
    padding: 10px 12px;
    font-size: 13px;
  }

  .software-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .software-card {
    padding: 12px;
  }

  .software-icon {
    float: none;
    text-align: center;
    margin: 0 0 10px 0;
  }

  .software-info {
    text-align: center;
  }
}

/* 全屏图片预览样式 */
.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.preview-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 20px;
}

.fullscreen-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transform-origin: center center;
  cursor: grab;
}

.fullscreen-preview-image:active {
  cursor: grabbing;
}

/* 底部控制栏 */
.preview-controls-bottom {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
}

.controls-wrapper {
  display: flex;
  gap: 15px;
  padding: 15px 25px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.controls-wrapper .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 45px;
  height: 45px;
  font-size: 18px;
}

.controls-wrapper .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: scale(1.1);
}

.controls-wrapper .el-button:focus {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-image-container {
    padding: 10px;
  }
  
  .preview-controls-bottom {
    bottom: 20px;
  }
  
  .controls-wrapper {
    gap: 10px;
    padding: 10px 20px;
  }
  
  .controls-wrapper .el-button {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .preview-controls-bottom {
    bottom: 15px;
  }
  
  .controls-wrapper {
    gap: 8px;
    padding: 8px 15px;
  }
  
  .controls-wrapper .el-button {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}
</style>
