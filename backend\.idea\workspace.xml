<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5f5fb2a7-7e3a-4a09-b2fe-33d2d2f7ee47" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/uploads/images/11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/11_1753955671758.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/11_1753955679313.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/23ac34ce-5651-449a-b59b-79673f532f9c.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/28c4211c-c644-48c1-8fde-f44131c82bef.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/3507619d-a97c-4ff7-95d6-43d5a4fee193.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/3d25d6ae-43c6-4672-9440-9280d0b3b907.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/3fb3cc64-358f-413b-a961-602261096bee.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/4cf4c618-ba26-4ac0-90e7-9f139b69a46b.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/5ebeb731-3f84-472d-9b6c-5c17b7553e97.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/71e46354-1a5c-4130-baf1-48767cc64025.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/77c11494-bcfa-4b15-b1c9-60c5f8c9d66e.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/89b8b81f-0bf7-412d-afb9-6648663a18a4.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/9a91fd8c-914d-45d5-9e3b-52548f662331.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/9ee7e096-107c-464e-b2b8-316e3800f311.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/d03978f2-fe6e-4be5-91b0-6268e70577c3.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/d463f12d-9195-483a-b4b3-d04a03b155ac.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/d9fc0980-a120-40d2-81d9-669ccf108193.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/e54b54f0-9fe1-4cc1-811b-0e0a8cce09a5.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/f365441d-db4b-4296-ace5-247fcf9675e6.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/【哲风壁纸】rap-动感篮球元素.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/images/【哲风壁纸】人类世界-动画场景.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/87176b15-5a1d-4d16-963c-c3fe7354e479.rar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/88201c6b-6be5-440a-9a7c-6eb1ba73c634.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/c33cfaee-9c88-4783-96b0-9508733c54a2.rar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/python-3.13.5-amd64 (2).exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/unlock解锁顽固删除不了的文件Unlocker1.9.2.0.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/白菜Cousor无限续杯v1.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploads/software/白菜Cousor无限续杯v1_1753955683683.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../database/software_download.sql" beforeDir="false" afterPath="$PROJECT_DIR$/../database/software_download.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/views/SoftwareList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/views/SoftwareList.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9" />
        <option name="localRepository" value="D:\dev\apache-maven-3.3.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30bOWcsS1NnS4B8WHxuFrN119Hj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.SoftwareApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Users/<USER>/test/软件下载网站git/software-download-website",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.fileTypes",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="SoftwareApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="software-download" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.software.SoftwareApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5f5fb2a7-7e3a-4a09-b2fe-33d2d2f7ee47" name="更改" comment="" />
      <created>1753892839113</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753892839113</updated>
      <workItem from="1753892840730" duration="6521000" />
      <workItem from="1753956194101" duration="1060000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>