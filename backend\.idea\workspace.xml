<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5f5fb2a7-7e3a-4a09-b2fe-33d2d2f7ee47" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/software/SoftwareApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/software/SoftwareApplication.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9" />
        <option name="localRepository" value="D:\dev\apache-maven-3.3.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\dev\apache-maven-3.3.9\apache-maven-3.3.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30bOWcsS1NnS4B8WHxuFrN119Hj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.SoftwareApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Users/<USER>/test/软件下载网站git/software-download-website",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.fileTypes",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="SoftwareApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="software-download" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.software.SoftwareApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5f5fb2a7-7e3a-4a09-b2fe-33d2d2f7ee47" name="更改" comment="" />
      <created>1753892839113</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753892839113</updated>
      <workItem from="1753892840730" duration="6521000" />
      <workItem from="1753956194101" duration="8000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>